require("dotenv").config();
const express = require("express");
const upload = require("multer")({ dest: "public/" });
const checkId = require("../utils/utils");
const Auth = require("../config/auth");
const router = express.Router();
const app = express();
const { queryRunner } = require("../dbConfig/queryRunner"); // Add this if not already imported
app.use("/users", require("../routes/users.js"));

// --- Forced update middleware ---
async function enforceForcedUpdate(req, res, next) {
  try {
    // Allow version check and health endpoints
    if (req.path === '/api/check-app-version' || req.path === '/api/health') return next();
    const platform = req.headers['x-platform'] || req.body?.platform || req.query?.platform;
    const appVersion = req.headers['x-app-version'] || req.body?.current_version || req.query?.current_version;
    if (!platform || !appVersion) return next();
    const versionRows = await queryRunner(`SELECT latest_version, force_update FROM app_versions WHERE platform = '${platform}' ORDER BY id DESC LIMIT 1`);
    if (!versionRows.length) return next();
    const { latest_version, force_update } = versionRows[0];
    if (force_update && appVersion !== latest_version) {
      return res.status(426).json({
        status: false,
        code: 'FORCED_UPDATE',
        message: 'A new version of the app is required. Please update from the Play Store or App Store.'
      });
    }
    next();
  } catch (err) {
    next();
  }
}

// Apply forced update middleware to all API routes
router.use(enforceForcedUpdate);

router.use((req, res, next) => {
  console.log(req.path);
  next();
});

const UsersController = require("../controllers/UsersController");
const MasterController = require("../controllers/MasterController");
const AdController = require("../controllers/AdController");
const AdminController = require("../controllers/AdminController");
const CompanyController = require("../controllers/CompanyController");
const WalletController = require("../controllers/WalletController");
const ProductController = require("../controllers/ProductController");
const ReelsController = require("../controllers/ReelsController");
const WebsiteController = require("../controllers/WebsiteController");
const UtilsController = require("../controllers/UtilsController.js");
const RewardController = require("../controllers/RewardController");
const {
  razorpayOrderCreation,
  razorpayPaymentSignatue,
} = require("../controllers/RazorpayController.js");

const {
  createPost,
  updatePost,
  listPostsController,
  listPremiumPostsController,
  saveUserPostLike,
  saveUserPostComment,
  getUserPosts,
  getPostComments,
  getSinglePost,
  deletePost,
} = require("../controllers/PostController.js");
const LudoController = require("../controllers/ludoController");
const { updateFcmToken,updateFcmTokenofuser , getFcmTokensOfBothUsers  } = require('../controllers/UsersController');
const { getAgoraTokenforCaller ,getAgoraTokenForCallee, getAgoraRtmToken } = require("../services/AgoraService");
const ChatController = require("../controllers/chatController");
const InboxController = require("../controllers/inboxController");
const exploreController = require('../controllers/explore_controller');
const VideoSDKController = require('../controllers/videosdk_controller');
const ContentCreatorPlanController = require("../controllers/ContentCreatorPlanController");
const ContentCreatorSubscriptionController = require("../controllers/ContentCreatorSubscriptionController");
const ContactController = require("../controllers/ContactController");
const ManualWalletController = require('../controllers/ManualWalletController');
const ManualUserPremiumController = require('../controllers/ManualUserPremiumController');
const ManualContentPremiumController = require('../controllers/ManualContentPremiumController');


// 1.in usage
router.get(
  "/getadpassbook/:userid",
  Auth.verifyToken,
  AdController.getAdPassBook
);
router.get("/getshots/:userid", ReelsController.getShots);
// free api for showing the publicshots
router.get("/getpublicshots", ReelsController.getPublicShots);

router.get("/termscondition", MasterController.getTermscondition);
router.get("/getindustry", AdminController.getAllIndustryList);
router.get("/getadminaccess", AdminController.getAdminAccessList);
router.get("/getallproduct", ProductController.getAllProductList);

// 2.authentication api's

router.post("/otplogin", UsersController.saveLoginOtp);
router.post("/logout", Auth.verifyToken, UsersController.userLogout);
router.post("/otpverify", UsersController.otpVerify);
router.post("/saveuserdetails", UsersController.saveUserDetails);
router.post("/updateuser", Auth.verifyToken, UsersController.updateUser);

// Token refresh endpoint
router.post("/refresh-token", Auth.refreshToken);

router.get("/ping", Auth.verifyToken, UsersController.ping);

// App version check endpoint
router.post("/check-app-version", async (req, res) => {
  try {
    const { current_version, platform } = req.body;
    const dbQuery = require('../dbConfig/queryRunner');
    const versionRows = await dbQuery.queryRunner(`SELECT latest_version, force_update, update_url, release_notes FROM app_versions WHERE platform = '${platform}' ORDER BY id DESC LIMIT 1`);
    if (!versionRows.length) {
      return res.status(200).json({ status: false, message: 'No version info found', data: null });
    }
    const { latest_version, force_update, update_url, release_notes } = versionRows[0];
    if (current_version !== latest_version) {
      return res.status(200).json({
        status: true,
        message: force_update ? 'A critical update is required to continue using the app.' : 'A new version is available.',
        data: { latest_version, force_update, update_url, release_notes }
      });
    }
    return res.status(200).json({ status: false, message: 'App is up to date', data: null });
  } catch (error) {
    res.status(500).json({ status: false, message: 'Error checking version', data: null });
  }
});

// 3.user status+notification
router.get(
  "/deleteStatus/:statusId",
  Auth.verifyToken,
  UsersController.deleteStatus
);
router.get(
  "/getOtherUserStatusList/:userId",
  Auth.verifyToken,
  UsersController.getOtherUserStatusList
);
router.get(
  "/getStatusViewersList/:statusId",
  Auth.verifyToken,
  UsersController.getStatusViewersList
);
router.post("/addStatus", Auth.verifyToken, UsersController.addStatus);
router.get(
  "/getCurrentUserStatusList/:userId",
  Auth.verifyToken,
  UsersController.getCurrentUserStatusList
);
router.post(
  "/addStatusViewers",
  Auth.verifyToken,
  UsersController.addStatusViewers
);
router.post(
  "/saveNotifications",
  Auth.verifyToken,
  UsersController.saveNotifications
);

router.get(
  "/getNotifications/:userId/:page/:limit",
  Auth.verifyToken,
  UsersController.getNotifications
);

const cpUpload = upload.fields([
  { name: "coverImage", maxCount: 1 },
  { name: "profileImage", maxCount: 1 },
]);

/// 04.E-commerce, CompanyController
/// addCompanyGst

router.get(
  "/checkCompanyNameExist/:name",
  Auth.verifyToken,
  CompanyController.checkCompanyNameExist
);
router.post(
  "/addCompanyGst",
  Auth.verifyToken,
  CompanyController.addCompanyGst
);
router.post(
  "/savecompany",
  Auth.verifyToken,
  cpUpload,
  CompanyController.saveCompany
);
router.post(
  "/createcompany",
  Auth.verifyToken,
  cpUpload,
  CompanyController.saveCompany
);

// updateCompanyNew
router.post(
  "/updateCompanyNew",
  Auth.verifyToken,
  CompanyController.updateCompanyNew
);
router.post(
  "/createnewcompany",
  Auth.verifyToken,
  CompanyController.createCompany
);
router.get(
  "/getcompanylist/:userId",
  Auth.verifyToken,
  CompanyController.getCompanyList
);
router.get(
  "/getallcompanylist/:userId",
  Auth.verifyToken,
  CompanyController.getAllCompanyList
);
router.get(
  "/getothercompanylist/:userId/:isfollow",
  Auth.verifyToken,
  CompanyController.getOtherCompanyList
);
router.get(
  "/getcompany/:id/:userId",
  Auth.verifyToken,
  CompanyController.getcompany
);
router.post(
  "/updatecompany",
  Auth.verifyToken,
  CompanyController.updateCompany
);
router.post(
  "/updatecompanydetails",
  Auth.verifyToken,
  CompanyController.updateCompanyDetails
);
router.post(
  "/deletecompany",
  Auth.verifyToken,
  CompanyController.deletecompany
);
router.get("/getAllCompanyPosts/:userId", CompanyController.getAllCompanyPosts);

//4.1. savepost,deletepost,companycontroller
//Add Post
router.post(
  "/savepost",
  Auth.verifyToken,
  cpUpload,
  CompanyController.savePost
);
router.get(
  "/getcompanypost/:companyId",
  Auth.verifyToken,
  CompanyController.getCompanyPost
);
router.post("/deletepost", Auth.verifyToken, CompanyController.deletePost);
router.get("/getPostDetails/:postId", CompanyController.getPostDetails);


//05.E-commerce,ProductController

router.get(
  "/getPopularProducts",
  Auth.verifyToken,
  ProductController.getPopularProducts
);
router.get(
  "/addProductToPopular/:Id",
  Auth.verifyToken,
  ProductController.addProductToPopular
);
router.get(
  "/deleteWishListItem/:Id",
  Auth.verifyToken,
  ProductController.deleteWishListItem
);
router.get("/getWishList/:Id", Auth.verifyToken, ProductController.getWishList);
router.post(
  "/addToWishList",
  Auth.verifyToken,
  ProductController.addToWishList
);
router.get(
  "/getSellerOrders/:userId",
  Auth.verifyToken,
  ProductController.getSellerOrders
);
router.get(
  "/getUserOrders/:userId",
  Auth.verifyToken,
  ProductController.getUserOrders
);
router.get(
  "/deleteCartAllItems/:userId",
  Auth.verifyToken,
  ProductController.deleteCartAllItems
);
router.get(
  "/deleteCartSingleItem/:cartId",
  Auth.verifyToken,
  ProductController.deleteCartSingleItem
);
router.post("/updateBargin", Auth.verifyToken, ProductController.updateBargin);
router.get(
  "/getBaraginItems/:id",
  Auth.verifyToken,
  ProductController.getBaraginItems
);
router.post("/addToBargain", Auth.verifyToken, ProductController.addToBargain);
router.post("/addAddress", Auth.verifyToken, ProductController.addAddress);
router.post("/placeOrder", Auth.verifyToken, ProductController.placeOrder);
router.post(
  "/updateSellerShippingDetails",
  Auth.verifyToken,
  ProductController.updateSellerShippingDetails
);
router.get(
  "/selectedDeliveryAddress/:id",
  Auth.verifyToken,
  ProductController.selectedDeliveryAddress
);
router.get(
  "/deliveryAddress/:id",
  Auth.verifyToken,
  ProductController.deliveryAddress
);
router.get(
  "/selectDeliveryAddress/:addressId/:userId",
  Auth.verifyToken,
  ProductController.selectDeliveryAddress
);
router.get(
  "/productcategory",
  Auth.verifyToken,
  ProductController.getProductCategory
);

//getCategoryProduct
router.get(
  "/getCategoryProduct",
  Auth.verifyToken,
  ProductController.getCategoryProduct
);



//addCategory
router.post("/addCategory", Auth.verifyToken, ProductController.addCategory);

router.post("/addproduct", Auth.verifyToken, ProductController.saveProduct);
router.post(
  "/savevproductsdetails",
  Auth.verifyToken,
  ProductController.saveProductDetails
);
router.post(
  "/updateproduct",
  Auth.verifyToken,
  checkId.checkId,
  ProductController.updateProduct
);
router.post(
  "/deleteProduct",
  Auth.verifyToken,
  checkId.checkId,
  ProductController.deleteProduct
);
router.post(
  "/getsearchresult",
  Auth.verifyToken,
  ProductController.getSearchResult
);

router.get(
  "/getallproduct",
  Auth.verifyToken,
  ProductController.getAllProductList
);
router.get(
  "/getProductlist/:companyId",
  Auth.verifyToken,
  ProductController.getProductList
);

router.get(
  "/getSearchCompany/:search",
  Auth.verifyToken,
  ProductController.getSearchCompany
);
router.get(
  "/getSearchProducts/:search",
  Auth.verifyToken,
  ProductController.getSearchProducts
);
router.get(
  "/getCartItems/:Id",
  Auth.verifyToken,
  ProductController.getCartItems
);
router.post("/addToCart", Auth.verifyToken, ProductController.addToCart);
router.get(
  "/getSingleProductById/:Id",
  Auth.verifyToken,
  ProductController.getSingleProductById
);
router.get(
  "/getProductListByAdvertiserId/:advertiserId",
  Auth.verifyToken,
  ProductController.getProductListByAdvertiserId
);
router.get(
  "/getProductByCategoryId/:Id",
  Auth.verifyToken,
  ProductController.getProductByCategoryId
);
router.get(
  "/productbyproductid/:id/:userid",
  Auth.verifyToken,
  ProductController.getProductByProductId
);
router.get(
  "/productbycategory/:categoryid/:userid",
  Auth.verifyToken,
  ProductController.getProductByCategory
);
router.get(
  "/productbycompanyid/:companyid",
  Auth.verifyToken,
  ProductController.getProductByCompanyId
);
router.get(
  "/newproduct/:userid",
  Auth.verifyToken,
  ProductController.getNewProduct
);
router.get("/getProductByUserid/:userid", ProductController.getProductByUserId);

//06.user,master,admin,wallet
router.get("/flashscreen/:filename", MasterController.getFlashScreen);
router.get(
  "/video/:filename",
  Auth.verifyToken,
  MasterController.getFlashScreen
);
router.get("/getuser/:id", Auth.verifyToken, UsersController.getUser);
router.get("/file/:filename", Auth.verifyToken, MasterController.getFile);
router.get("/photo/:id", Auth.verifyToken, MasterController.getPhoto);
router.get("/QRCode/:id", Auth.verifyToken, MasterController.getQRCode);

router.get(
  "/getalladmins/:createdby",
  Auth.verifyToken,
  AdminController.getAllAdminList
);
router.post("/saveadmin", Auth.verifyToken, AdminController.saveAdmin);
router.post("/updateadmin", Auth.verifyToken, AdminController.updateAdmin);
router.post("/deleteadmin", Auth.verifyToken, AdminController.deleteAdmin);
router.post("/addfunds", Auth.verifyToken, WalletController.saveFunds);
router.get("/getfunds/:id", Auth.verifyToken, WalletController.getFunds);
router.get(
  "/admin/getallchannels/:page/:limit",
  AdminController.getallChannels
);

router.post("/withdrawFund", Auth.verifyToken, WalletController.withdrawFund);
router.post(
  "/withdrawFundFromWallet",
  Auth.verifyToken,
  WalletController.updateWithdrawFund
);

// New Withdrawal Routes
const WithdrawalController = require("../controllers/WithdrawalController");

// Withdrawal settings and eligibility
router.get("/withdrawal-settings", Auth.verifyToken, WithdrawalController.getWithdrawalSettings);
router.post("/check-withdrawal-eligibility", Auth.verifyToken, WithdrawalController.checkWithdrawalEligibility);

// Wallet withdrawal
router.post("/process-wallet-withdrawal", Auth.verifyToken, WithdrawalController.processWalletWithdrawal);

// Referral withdrawal
router.post("/process-referral-withdrawal", Auth.verifyToken, WithdrawalController.processReferralWithdrawal);

// Channel withdrawal
router.post("/process-channel-withdrawal", Auth.verifyToken, WithdrawalController.processChannelWithdrawal);

// Withdrawal history and stats
router.get("/withdrawal-history/:userId", Auth.verifyToken, WithdrawalController.getWithdrawalHistory);
router.get("/withdrawal-stats/:userId", Auth.verifyToken, WithdrawalController.getWithdrawalStats);

//07.wallet,cart..etc

router.get(
  "/payUsingWallet/:userId/:requestAmount",
  Auth.verifyToken,
  ProductController.payUsingWallet
);
router.get(
  "/getsavedsearch/:userid",
  Auth.verifyToken,
  ProductController.getSearchList
);
router.get(
  "/getcartitembyuser/:userid",
  Auth.verifyToken,
  ProductController.getCartItemByUser
);
router.get(
  "/getproductads/:userid",
  Auth.verifyToken,
  ProductController.getProductAds
);
router.get(
  "/getrecentlyviewproduct/:userid",
  Auth.verifyToken,
  ProductController.getRecentlyViewProducts
);
router.get(
  "/deletecartitembyuser/:cartid",
  Auth.verifyToken,
  ProductController.deleteCartItembyCartId
);
router.get(
  "/getorderlist/:userid",
  Auth.verifyToken,
  ProductController.getOrderList
);
router.get(
  "/getbargainrequestbyuser/:userid",
  Auth.verifyToken,
  ProductController.getBargainRequestByUser
);
router.get(
  "/getbargainrequestbycompany/:userid",
  Auth.verifyToken,
  ProductController.getBargainRequestByCompany
); // not in used
router.get(
  "/getuseraddress/:userid",
  Auth.verifyToken,
  ProductController.getUserAddressByUser
);
router.get(
  "/getwishlistproduct/:userid",
  Auth.verifyToken,
  ProductController.getWishlistProductByUserId
);
router.get(
  "/getcouponsbycategory/:category",
  Auth.verifyToken,
  ProductController.getCouponsbyCategory
);
router.get(
  "/getordersbyuserid/:userid",
  Auth.verifyToken,
  ProductController.getOrdersByUserId
);
router.get(
  "/deleteuseraddress/:id",
  Auth.verifyToken,
  ProductController.deleteUserAddress
);

router.post("/savecartitem", Auth.verifyToken, ProductController.saveCartItem);
router.post(
  "/savesearchproduct",
  Auth.verifyToken,
  ProductController.saveSearchProduct
);
router.post(
  "/searchitems",
  Auth.verifyToken,
  ProductController.saveSearchProduct
);
router.post(
  "/getsimilarsearch",
  Auth.verifyToken,
  ProductController.getSimilarsearch
);
router.post(
  "/savebargainrequest",
  Auth.verifyToken,
  ProductController.saveBargainRequest
);
router.post(
  "/saveuseraddress",
  Auth.verifyToken,
  ProductController.saveUserAddress
);
router.post(
  "/saveproductorder",
  Auth.verifyToken,
  ProductController.saveProductOrder
);
router.post(
  "/updatebarginstatus",
  Auth.verifyToken,
  ProductController.updateBarginStatus
);
router.post(
  "/updatecartproduct",
  Auth.verifyToken,
  ProductController.updateCartProduct
);

//08.Adds
//AD model
router.get(
  "/getAdForShortVideo/:userId",
  Auth.verifyToken,
  AdController.getAdForShortVideo
);

router.get("/getadmodels", AdController.getAdModels);
router.get("/getAdDetailsForQr/:adId", AdController.getAdDetailsForQr);
router.get(
  "/gettargetprofession",
  Auth.verifyToken,
  AdController.getTargetProfession
);

router.get(
  "/gettargetprofessions",
  AdController.getTargetProfession
);

router.get("/gettargetareas", Auth.verifyToken, AdController.getTargetAreas);
router.get("/getbuttons", Auth.verifyToken, AdController.getButtons);
router.get(
  "/getCompanyButton",
  Auth.verifyToken,
  AdController.getCompanyButton
);
router.get("/getanimations", Auth.verifyToken, AdController.getAnimations);

router.post(
  "/savefirstpageadmodel",
  Auth.verifyToken,
  AdController.saveFirstAdModel
);
const adUpload = upload.fields([{ name: "adFile", maxCount: 1 }]);

router.post(
  "/savesecondpageadmodel",
  Auth.verifyToken,
  adUpload,
  checkId.checkId,
  AdController.saveSecondPageAdmodel
);
router.post(
  "/savethirdpageadmodel",
  Auth.verifyToken,
  checkId.checkId,
  AdController.saveThirdPageAdmodel
);

// router.post('/savefourpageadmodel',checkId.checkId,AdController.saveFourPageAdmodel)
// router.post('/savefivepageadmodel',checkId.checkId,AdController.saveFivePageAdmodel)
// router.get('/getads', AdController.getAllSaveAds)

//08.1 demo , coupoon , all adds
router.post("/requestdemo", Auth.verifyToken, AdController.requestdemo);
router.get("/getrequestdemoads/:userid", AdController.getrequestdemoads);
router.post("/validatecoupon", Auth.verifyToken, AdController.validateCoupon);
router.get("/getcoupon", AdController.getCoupon);
router.get("/getalladds/:userId", Auth.verifyToken, AdController.getAdds);
router.post("/getallads", Auth.verifyToken, AdController.getFilterAds);

//08.2 add,save adview,like,amount
router.get(
  "/getadviewsandlikes/:userid/:adid",
  Auth.verifyToken,
  AdController.getAdViewsAndLike
);
router.post(
  "/saveadviewsandlikes",
  Auth.verifyToken,
  AdController.saveAdViewsAndLikes
);
router.post(
  "/saveadlikeamount",
  Auth.verifyToken,
  AdController.saveAdLikeAmount
);
router.post(
  "/saveadviewamount",
  Auth.verifyToken,
  AdController.saveAdViewAmount
);
router.post("/saveadamount", Auth.verifyToken, AdController.addAmountToWallet);
router.get(
  "/getmylikead/:userId/:page",
  Auth.verifyToken,
  AdController.getMyLikesAds
);
router.post("/getadview", Auth.verifyToken, AdController.getAdView);

//09.celebration/master/business_adds..etc
/** celebration adds */
router.post(
  "/savecelebrationadds",
  Auth.verifyToken,
  adUpload,
  AdController.saveCelebrationAddModels
);
router.post("/getmasterads", Auth.verifyToken, AdController.getMasterFilterAds);
router.post("/getmasteradsPagination", AdController.getMasterAdsPagination);
router.post(
  "/getcelebrationads",
  Auth.verifyToken,
  AdController.getCelebrationAds
);
router.post(
  "/savebussinessad",
  Auth.verifyToken,
  adUpload,
  AdController.saveBussinessAdModel
);
router.post(
  "/savecelebrationadview",
  Auth.verifyToken,
  AdController.saveCelebrationAdView
);
router.post("/getbussinessads", Auth.verifyToken, AdController.getBuisnessAds);
router.post("/getadhubads", Auth.verifyToken, AdController.getAdHubsAds);
router.post(
  "/savefallowcompany",
  Auth.verifyToken,
  AdController.saveFallowCompany
);

router.get(
  "/getlossviewamount/:adId",
  Auth.verifyToken,
  AdController.getLossViewAmount
);
router.get(
  "/getfallowcompany/:userid",
  Auth.verifyToken,
  AdController.getFallowCompany
);
router.get(
  "/getaddetails/:adid/:userid",
  Auth.verifyToken,
  AdController.getAdDetails
);
router.get(
  "/getOrderTracking/:adid",
  Auth.verifyToken,
  AdController.getOrderTracking
);
router.get("/getaddetailsforViedeo/:adid/", AdController.getAdDetailsforVideo);
router.get(
  "/getLastaddetails/:companyId/:userid",
  Auth.verifyToken,
  AdController.getLastAdDetails
);
router.get(
  "/getuserlikeads/:userid",
  Auth.verifyToken,
  AdController.getUserLikeAds
);
router.get(
  "/getlikeadsbycompany/:companyuserid",
  Auth.verifyToken,
  AdController.getLikeAdsbyCompanyUserId
);
router.get(
  "/getblockadbycompany/:userid",
  Auth.verifyToken,
  AdController.getBlockAdbyCompany
);
router.get(
  "/getblockadcompanybyuser/:userid",
  Auth.verifyToken,
  AdController.getBlockAdCompanyByUser
);

router.post(
  "/saveadpausecountinuestatus",
  Auth.verifyToken,
  AdController.saveAdPauseCountinueStatus
);
router.post("/adblock", Auth.verifyToken, AdController.saveBlockad);
router.post("/getgraphdata", Auth.verifyToken, AdController.getGraphData);


//10.Reels,shorts
//Reels
router.get("/getvideocatagory", ReelsController.getVideoCatagory);
router.post("/uploadshot", Auth.verifyToken, ReelsController.uploadShot);
router.post(
  "/saveChannelWithdrawRequest",
  Auth.verifyToken,
  ReelsController.saveChannelWithdrawRequest
);
///getShortById
router.get("/getShortById/:userId/:reelId", ReelsController.getShortById);
///generatePresignedUrl
router.post("/generatePresignedUrl",Auth.verifyToken, ReelsController.generatePresignedUrl);

router.post(
  "/savevideodetails",
  Auth.verifyToken,
  ReelsController.saveVideoDetails
);
router.post("/saveVideoLike", Auth.verifyToken, ReelsController.saveVideoLike);
router.get("/getwatchingvideo/:userid", ReelsController.getWatchingVideo);

router.get(
  "/getUserVideoViewLikeDetails/:userId/:page",
  ReelsController.getUserVideoViewLikeDetails
);
router.get(
  "/getvideos/:userid/:categoryid/:offset",
  ReelsController.getAllVideos
);

//free aapi for tiptube showing in homepage
router.get(
  "/getpublicvideos/:categoryid/:offset",
  ReelsController.getPublicVideos
);

router.get(
  "/getrecentlyuploadedvideo/:userid",
  Auth.verifyToken,
  ReelsController.getRecentlyUploaded
);
router.get(
  "/getrecentlyuploadedshort/:userid",
  Auth.verifyToken,
  ReelsController.getRecentlyUploadedShort
);
router.get(
  "/getallrecentlyuploadedvideo/:page",
  ReelsController.getAllRecentlyUploaded
);
router.get(
  "/getallrecentlyuploadedshort/:page",
  ReelsController.getAllRecentlyUploadedShort
);

router.post("/searchfuntube", ReelsController.searchfuntube);

router.get(
  "/getwatchinglatervideo/:userid",
  Auth.verifyToken,
  ReelsController.getWatchingLaterVideo
);
router.get("/getvideo/:videoid/:userid", ReelsController.getVideoDetails);

// Add endpoint for deep linking video access (matches frontend expectation)
router.get("/video/:videoId", ReelsController.getSingleVideo);

router.post(
  "/savevideocomment",
  Auth.verifyToken,
  ReelsController.saveVideoComment
);
router.post(
  "/savevideocommentlike",
  Auth.verifyToken,
  ReelsController.saveVideoCommentLike
);
router.get(
  "/getcommentsofvideos/:userId/:videoId",
  Auth.verifyToken,
  ReelsController.getcommentsofvideos
);

router.get(
  "/getCommentOfVideo/:videoId/:page/:limit",
  Auth.verifyToken,
  ReelsController.getCommentOfVideo
);
router.get(
  "/deletecommentbyid/:commentId",
  Auth.verifyToken,
  ReelsController.deletecommentbyid
);
router.get(
  "/getvideobychannel/:videoType/:channelid/:userid",
  Auth.verifyToken,
  ReelsController.getVideoByChannel
);
router.get(
  "/deleteVideo/:videoId",
  Auth.verifyToken,
  ReelsController.deleteVideo
);
router.post("/editVideo", Auth.verifyToken, ReelsController.editVideo);

//10.1 channels , analytics
//chanels
router.post("/savemychannel", Auth.verifyToken, ReelsController.saveMyChannels);
router.get("/getchannelbyuserid/:userid", ReelsController.getMyChannelList);
router.get("/channel/:id/:userid", ReelsController.getChannelByID);
router.post("/updatechanel", Auth.verifyToken, ReelsController.updateChannel);
router.get("/getpopularvideo/:userid", ReelsController.getPopularVideo);

//channel Analytics
router.get(
  "/analytics/:channelId",
  Auth.verifyToken,
  ReelsController.getAnalytics
);

// General Analytics
const AnalyticsController = require('../controllers/AnalyticsController');
router.post("/track-analytics", Auth.verifyToken, AnalyticsController.trackAnalytics);
router.get("/analytics", Auth.verifyToken, AnalyticsController.getAnalytics);

// follow channel
router.get(
  "/getlistoffollowedchannelbyuser/:userid",
  ReelsController.getListOfFollowedChannelByUser
);
router.get(
  "/gettrendingvideoofallchannel/:userid",
  ReelsController.getTrendingVideosOfAllChannel
);
router.get(
  "/getlistofweekvideoofchannelwhichfollowbyuser/:userid",
  ReelsController.getWeekVideoAllChannelFollowedByUser
);
router.get(
  "/getlistofmonthvideoofchannelwhichfollowbyuser/:userid",
  ReelsController.getMonthVideoAllChannelFollowedByUser
);
router.post(
  "/savechanneldetails",
  Auth.verifyToken,
  ReelsController.saveChannelDetails
);

router.post(
  "/saveChannelFollowers",
  Auth.verifyToken,
  ReelsController.saveChannelFollowers
);
router.get(
  "/getpopularshort/:videoType/:userid",
  ReelsController.getPopularShort
);
//channelShortsVideos
router.get("/channelShortsVideos/:userid", ReelsController.channelShortsVideos);
router.post(
  "/createplaylist",
  Auth.verifyToken,
  ReelsController.createPlayList
);
router.get(
  "/playlistbychannelid/:channelid",
  ReelsController.getPlayListByChannelById
);
router.post("/getTransactions", ReelsController.getTransactions);

//11.chat api's
//Chat apis
router.post(
  "/saveuserdevicetoken",
  Auth.verifyToken,
  UsersController.saveUserDeviceToken
);
router.post("/sendmessage", Auth.verifyToken, UsersController.sendmessage);
router.post(
  "/deletemessages",
  Auth.verifyToken,
  UsersController.deletemessages
);
router.post("/saveticks", Auth.verifyToken, UsersController.saveticks);
router.post(
  "/blockChatUser",
  Auth.verifyToken,
  UsersController.updateBlockUser
);
router.post(
  "/deletechatforme",
  Auth.verifyToken,
  UsersController.deletechatforme
);
router.post(
  "/deletechatforeveryone",
  Auth.verifyToken,
  UsersController.deletechatforEveryone
);

// New route for fetching messages
router.get("/messages", Auth.verifyToken, ChatController.getMessages);

// New routes for unread message count and marking messages as read
router.get("/chat/unread-count/:userId", Auth.verifyToken, ChatController.getUnreadMessageCount);
router.post("/chat/mark-as-read", Auth.verifyToken, ChatController.markMessagesAsRead);

// Inbox API routes
router.get("/inbox/:userId", Auth.verifyToken, InboxController.getInboxMessages);
router.put("/inbox/mark-read/:messageId", Auth.verifyToken, InboxController.markMessageAsRead);
router.get("/inbox/unread-count/:userId", Auth.verifyToken, InboxController.getUnreadCount);

//12.Audio
// Audio Calling
router.post("/audioCalling", Auth.verifyToken, UsersController.audioCalling);

router.get(
  "/getsentnotification/:userid",
  Auth.verifyToken,
  UsersController.getSentNotification
);
router.get(
  "/getreceivednotification/:userid",
  Auth.verifyToken,
  UsersController.getReceivedNotification
);
router.get(
  "/getmessages/:userid",
  Auth.verifyToken,
  UsersController.getMessages
);
router.get(
  "/getmessage/:loginuserid/:chattinguserid",
  Auth.verifyToken,
  UsersController.getMessage
);
router.get(
  "/getmuteandblockusers/:userid",
  Auth.verifyToken,
  UsersController.getMuteAndBlockUsers
);
router.get(
  "/clearallchat/:loginuserid/:chattinguserid",
  Auth.verifyToken,
  UsersController.clearAllchat
);
router.get(
  "/seenallmessage/:loginuserid/:chattinguserid",
  Auth.verifyToken,
  UsersController.seenAllMessage
);
router.get(
  "/getunseenmessagecount/:loginuserid",
  Auth.verifyToken,
  UsersController.getUnSeenMessageCount
);
router.post(
  "/updatedevicetoken",
  Auth.verifyToken,
  UsersController.updateDeviceToken
);

router.get(
  "/getconfig/:configkey",
  Auth.verifyToken,
  UsersController.getConfigValue
);

//13.user referals
////user referals
router.get(
  "/generateReferalId/:userId",
  Auth.verifyToken,
  UsersController.generateReferalId
);
//checkReferalCodeValid
router.post(
  "/checkReferalCodeValid",
  Auth.verifyToken,
  UsersController.checkReferalCodeValid
);
router.post(
  "/saveReferWithdrawRequest",
  Auth.verifyToken,
  UsersController.saveReferWithdrawRequest
);
//getTransactionsOfReferalWithdrawRequests
router.post(
  "/getTransactionsOfReferalWithdrawRequests",
  Auth.verifyToken,
  UsersController.getTransactionsOfReferalWithdrawRequests
);

//14.Admin Panel
//Admin Panel Services
router.post("/admin/authenticate", AdminController.authenticate);
router.post("/admin/updteVerifyUser", AdminController.updteVerifyUser);
router.post("/admin/adUser", AdminController.addUser);
router.get("/admin/getAllAdminUsers/:userid", AdminController.getAllAdminUsers);
router.get(
  "/admin/getallusers/:page/:limit",
  Auth.verifyToken,
  AdminController.getallusers
);
//getAllVideos
router.get("/admin/getAllVideos/:page/", AdminController.getAllVideos);
router.get(
  "/admin/getVideoBySearch/:search/",
  AdminController.getVideoBySearch
);
//deleteVideoById
router.get("/admin/deleteVideoById/:id/", AdminController.deleteVideoById);
// ===== DEPRECATED ADMIN WITHDRAWAL ROUTES (OLD LOGIC) =====
// router.get(
//   "/admin/getwithdrawRequest/:page/:limit",
//   AdminController.getwithdrawRequest
// );
// router.post(
//   "/admin/withdrawFundFromWallet",
//   Auth.verifyToken,
//   WalletController.updateWithdrawFund
// );
// router.post(
//   "/admin/changeStatusPaid",
//   Auth.verifyToken,
//   WalletController.changeStatusPaid
// );
// router.get(
//   "/admin/getPaidFunds",
//   Auth.verifyToken,
//   WalletController.getPaidFunds
// );

// ===== NEW PROFESSIONAL ADMIN WITHDRAWAL ROUTES =====
// Wallet Withdrawals
router.get("/admin/withdrawals/wallet", Auth.verifyToken, AdminController.getWalletWithdrawals); // TODO: implement
router.patch("/admin/withdrawals/wallet/:id/status", Auth.verifyToken, AdminController.updateWalletWithdrawalStatus); // TODO: implement
router.get("/admin/withdrawals/wallet/export", Auth.verifyToken, AdminController.exportWalletWithdrawals); // TODO: implement

// Referral Withdrawals
router.get("/admin/withdrawals/referral", Auth.verifyToken, AdminController.getReferralWithdrawals); // TODO: implement
router.patch("/admin/withdrawals/referral/:id/status", Auth.verifyToken, AdminController.updateReferralWithdrawalStatus); // TODO: implement
router.get("/admin/withdrawals/referral/export", Auth.verifyToken, AdminController.exportReferralWithdrawals); // TODO: implement

// Coupon Withdrawals
router.get("/admin/withdrawals/coupon", Auth.verifyToken, AdminController.getCouponWithdrawals); // TODO: implement
router.patch("/admin/withdrawals/coupon/:id/status", Auth.verifyToken, AdminController.updateCouponWithdrawalStatus); // TODO: implement
router.get("/admin/withdrawals/coupon/export", Auth.verifyToken, AdminController.exportCouponWithdrawals); // TODO: implement

// Channel/Content Creator Withdrawals
router.get("/admin/withdrawals/channel", Auth.verifyToken, AdminController.getChannelWithdrawals); // TODO: implement
router.patch("/admin/withdrawals/channel/:id/status", Auth.verifyToken, AdminController.updateChannelWithdrawalStatus); // TODO: implement
router.get("/admin/withdrawals/channel/export", Auth.verifyToken, AdminController.exportChannelWithdrawals); // TODO: implement

//15.
///admin ecommmerce
router.get("/admin/getAllOrders/:page/", AdminController.getAllOrders);
router.get("/admin/getAllProducts/:page/", AdminController.getAllProducts);
router.get("/admin/verifyCompany/:id/", AdminController.verifyCompany);

//16.
//
router.get("/geteducation", Auth.verifyToken, UsersController.getEducations);
router.get("/getlanguages", Auth.verifyToken, UsersController.getLanguages);
router.get("/getinterests", Auth.verifyToken, UsersController.getInterests);
router.get("/getprofessions", Auth.verifyToken, UsersController.getProfessions);
router.get("/getlanguagesnotoken", UsersController.getLanguages);
router.get("/getinterestsnotoken", UsersController.getInterests);

//17.
/// website
router.post("/savewebsitemessage", WebsiteController.saveWebsiteMessage);
router.get("/googleplace", WebsiteController.googleplace);
router.get("/googleplacedetails", WebsiteController.googlePlaceDetails);
router.get(
  "/checkadpendingbalance/:id",
  WebsiteController.checkAdPendingBalance
);
router.get(
  "/getQrAdWebByAdIdForAdvertiser/:adId",
  WebsiteController.getQrAdWebByAdIdForAdvertiser
);

router.get("/getQrAdWebAllForAdmin", WebsiteController.getQrAdWebAllForAdmin);
router.post("/insertQRScanWebData", WebsiteController.insertQRScanWebData);

//18.utils,users
///utils
router.post("/sendNotification", UtilsController.sendNotification);
router.post("/sendNotificationToAll", UtilsController.sendNotificationToAll);
router.get("/getpremium", CompanyController.getPremium);
router.get("/getpremium-plans", CompanyController.getPremiumPlans);
app.post("/payment-callback", CompanyController.paymentCallback);


/**
 *@user routes here
 *
 */
router.post("/users", Auth.verifyToken, UsersController.allUsers);
router.post("/allusers", Auth.verifyToken, UsersController.allUsersall);

//19.zegocloud , user-calls
router.get(
  "/getZegoCloudDetails",
  Auth.verifyToken,
  UsersController.getZegoCloudDetails
);

router.post("/block-user", Auth.verifyToken, UsersController.blockUser);
router.post("/unblock-user", Auth.verifyToken, UsersController.unBlockUser);
router.post("/blocked-users", Auth.verifyToken, UsersController.blockedList);
router.post("/save-call", Auth.verifyToken, UsersController.saveCallDetails);
router.post("/recent-calls", Auth.verifyToken, UsersController.getRecentCalls);

/* temp */
router.post(
  "/blocked-unblocked-list",
  UsersController.getBlockedAndUnblockedUsers
);


//20.coupon
/**
 coupon apis
 */
router.get("/coupons", Auth.verifyToken, UsersController.getCoupons);
router.post("/refer", Auth.verifyToken, UsersController.processReferral);

router.get(
  "/referral/details/:userId",
  Auth.verifyToken,
  UsersController.getReferralDetails
);
router.post("/apply-coupon", Auth.verifyToken, UsersController.applyCoupon);


//21.premium , check, active , withdraw
// Define the route for upgrading premium
router.post(
  "/upgrade-premium",
  Auth.verifyToken,
  UsersController.upgradePremium
);

router.get("/user-premium-plans/:userId", Auth.verifyToken, UsersController.getUserPremiumPlans);

router.get("/user-premium-plans-count/:userId", Auth.verifyToken, UsersController.getUserPremiumPlansCount);

router.post("/activate-premium-plan", Auth.verifyToken, UsersController.activatePremiumPlan);

router.get(
  "/check-premium/:userId",
  Auth.verifyToken,
  UsersController.checkPremiumStatus
);

router.post("/withdraw", Auth.verifyToken, UsersController.withdrawFunds);


//22.razorpay
/**
  RAZORPAY ROUTES
 */
router.post("/razorpay-order", Auth.verifyToken, razorpayOrderCreation);

router.get(
  "/razorpay-details",
  Auth.verifyToken,
  UsersController.getRazorpayDetails
);
//TEST KEYS FOR LOCAL TESTING
router.get(
  "/razorpay-details-test",
  Auth.verifyToken,
  UsersController.getRazorpayDetailsTest
);

//TEST KEYS FOR LOCAL TESTING (NO AUTH REQUIRED)
router.get(
  "/razorpay-details-test-public",
  UsersController.getRazorpayDetailsTest
);

router.post(
  "/razorpay-verification",
  Auth.verifyToken,
  razorpayPaymentSignatue
);


// SUBSCRIPTION ROUTES
const SubscriptionController = require("../controllers/SubscriptionController");

// Get available subscription plans
router.get("/subscription-plans", Auth.verifyToken, SubscriptionController.getSubscriptionPlans);
// Get subscription plans with GST for Razorpay checkout
router.get("/subscription-plans-with-gst", Auth.verifyToken, SubscriptionController.getSubscriptionPlansWithGST);
// Create a new subscription
router.post("/subscriptions/create", Auth.verifyToken, SubscriptionController.createSubscription);
// Cancel a subscription
router.post("/subscriptions/cancel", Auth.verifyToken, SubscriptionController.cancelSubscription);
// Get user's subscription status
router.get("/subscriptions/status/:userId", Auth.verifyToken, SubscriptionController.getSubscriptionStatus);
// Get Razorpay details for frontend
router.get("/razorpay-details", Auth.verifyToken, SubscriptionController.getRazorpayDetails);
// Razorpay webhook for handling subscription events
router.post("/razorpay-webhook", SubscriptionController.handleWebhook);

// test plans for ubscriotoin :
router.get("/subscription-plans-test", Auth.verifyToken, SubscriptionController.getSubscriptionPlansTest);
// Get test subscription plans with GST for Razorpay checkout
router.get("/subscription-plans-test-with-gst", Auth.verifyToken, SubscriptionController.getSubscriptionPlansTestWithGST);
router.post("/subscriptions/create-test", Auth.verifyToken, SubscriptionController.createSubscriptionTest);
router.post("/subscriptions/cancel-test", Auth.verifyToken, SubscriptionController.cancelSubscriptionTest);

//23.media, getdetails
router.get("/media", Auth.verifyToken, CompanyController.getMediaFiles);

/*
  POST ROUTES (CREATE,UPDATE,DELETE, LIST, LIKES, COMMENTS, FOLLOW)
*/

router.get("/categories", Auth.verifyToken, CompanyController.getCategories);

router.post("/post", Auth.verifyToken, createPost);

router.post("/update-post", Auth.verifyToken, updatePost);

router.post("/list-posts", Auth.verifyToken, listPostsController);

// Updated route for premium posts (GET, no token required)
router.get("/list-premium-posts", listPremiumPostsController);

router.post("/post/delete", Auth.verifyToken, deletePost);

router.post("/save-user-post-like", Auth.verifyToken, saveUserPostLike);

router.post("/save-user-post-comment", Auth.verifyToken, saveUserPostComment);

router.post("/follow-user", Auth.verifyToken, UsersController.followUser);

router.get(
  "/follow/:type/:user_id",
  Auth.verifyToken,
  UsersController.getFollowersOrFollowings
);
//post according to users
router.get("/users/:userId/posts", Auth.verifyToken, getUserPosts);

// Consolidated profile API - gets user data, posts, followers, following in one call
router.get("/users/:userId/profile", Auth.verifyToken, UsersController.getConsolidatedProfile);

router.get("/posts/:postId/comments", Auth.verifyToken, getPostComments);

// Get single post by ID (for deep linking)
router.get("/post/:postId", getSinglePost);

// Alternative endpoint for backward compatibility
router.get("/getpostdetails/:postId", getSinglePost);

//23.1 banners

//home-india-banners
router.get("/get-home-banners-india", Auth.verifyToken, CompanyController.getMediaFilesIndia);
//tip-call-india-banners
router.get("/get-tip-call-banners-india", Auth.verifyToken, CompanyController.getMediaFilesIndiaTipCall);

//24.callroutes
/*
  CALL ROUTES
*/

router.post("/call", Auth.verifyToken, UsersController.initiateCall);

//24.1. Voice Call Routes (New subscription-based system)
const VideoSDKCallController = require('../controllers/VideoSDKCallController');
router.post("/voice-call", Auth.verifyToken, VideoSDKCallController.initiateVideoSDKCall);
router.get("/voice-call/balance/:userId", Auth.verifyToken, VideoSDKCallController.getCallBalance);
router.get("/voice-call/history/:userId", Auth.verifyToken, VideoSDKCallController.getCallHistory);

//24.2. Video Call Routes (New subscription-based system)
const VideoCallController = require('../controllers/VideoCallController');
router.post("/video-call", Auth.verifyToken, VideoCallController.initiateVideoCall);
router.get("/video-call/balance/:userId", Auth.verifyToken, VideoCallController.getVideoCallBalance);
router.get("/video-call/history/:userId", Auth.verifyToken, VideoCallController.getVideoCallHistory);

router.post('/update-fcm-token',Auth.verifyToken, updateFcmToken);

router.get('/get-fcm-token/:userId',Auth.verifyToken, updateFcmTokenofuser);

router.post('/fcm-tokens-of-both-users',Auth.verifyToken, getFcmTokensOfBothUsers)
 // agora toke n: 
router.post("/get-agora-token/caller", async (req, res) => {
  try {
    const { uid } = req.body;
    
    // Validate UID
    if (!uid || !Number.isInteger(Number(uid))) {
      return res.status(400).json({
        error: "Valid integer UID is required"
      });
    }

    const tokenData = await getAgoraTokenforCaller(Number(uid));
    res.json(tokenData);
  } catch (error) {
    res.status(error.message.includes("environment variables") ? 500 : 400).json({
      error: error.message,
    });
  }
});

router.post("/get-agora-token/callee", async (req, res) => {
  try {
    const { uid, channelName } = req.body;
    
    // Validate inputs
    if (!uid || !Number.isInteger(Number(uid))) {
      return res.status(400).json({
        error: "Valid integer UID is required"
      });
    }
    if (!channelName || typeof channelName !== 'string' || channelName.trim() === '') {
      return res.status(400).json({
        error: "Valid channelName is required"
      });
    }

    const tokenData = await getAgoraTokenForCallee(Number(uid), channelName.trim());
    res.json(tokenData);
  } catch (error) {
    res.status(error.message.includes("environment variables") ? 500 : 400).json({
      error: error.message,
    });
  }
});


// --- New RTM Token Route ---
router.post("/get-rtm-token", async (req, res) => {
  try {
    const { uid } = req.body; // This is for RTM, expects a string user ID

    // Validate UID for RTM
    // RTM user IDs are typically strings. Your client sends it as uid.
    if (!uid || typeof uid !== 'string' || uid.trim() === '') {
      return res.status(400).json({
        error: "Valid non-empty string User ID (uid) is required for RTM token"
      });
    }

    // Call the RTM token generation service
    const rtmTokenData = await getAgoraRtmToken(uid); // Pass the string uid
    
    // The client expects { token: string, userId: string }
    res.json(rtmTokenData);

  } catch (error) {
    console.error("Error in /get-rtm-token route:", error.message);
    // Distinguish between server configuration errors and bad requests
    res.status(error.message.includes("environment variables") ? 500 : 400).json({
      error: error.message,
    });
  }
});
//router.post("/video-call", Auth.verifyToken, UsersController.initiateVideoCall);



router.get(
  "/missed-calls/:userId?",
  Auth.verifyToken,
  UsersController.getMissedCallList
);

router.get(
  "/user/:user_id/call-status",
  Auth.verifyToken,
  UsersController.checkUserAvailability
);

router.get(
  "/user/call-status",
  Auth.verifyToken,
  UsersController.checkUserAvailability2
);

//24.1. videosdk, videosdk_token
router.post('/generate-token/videosdk', VideoSDKController.generateToken);
router.post('/create-meeting/videosdk', VideoSDKController.createMeeting);
router.post('/validate-meeting/videosdk/:meetingId', VideoSDKController.validateMeeting);
router.post('/deactivate-room/videosdk', VideoSDKController.deactivateRoom);

//25.auth/register
// Add /auth/register endpoint
router.post("/auth/register", async (req, res) => {
  const { name, email, password } = req.body;
  if (!name || !email || !password) {
    return res.status(400).json({ message: "Name, email, and password are required" });
  }

  try {
    // Check if email already exists
    const checkQuery = "SELECT id FROM users WHERE email = ?";
    const existingUser = await queryRunner(checkQuery, [email]);
    if (existingUser.length > 0) {
      return res.status(409).json({ message: "Email already registered" });
    }

    // Insert new user (password should be hashed in production)
    const insertQuery = "INSERT INTO users (name, email, password) VALUES (?, ?, ?)";
    const result = await queryRunner(insertQuery, [name, email, password]);
    
    res.status(201).json({ 
      message: "User registered successfully", 
      userId: result.insertId 
    });
  } catch (err) {
    console.error("Register error:", err);
    res.status(500).json({ message: "Error registering user", error: err.message });
  }
});

//26.Pubscale integration:
router.get(
  "/payments/offers/pubscale",
  ProductController.handlePubscaleCallback
);


//27.Content-Premium-plans and paidview video api:
router.post(
  "/upgrade-content-premium",
  Auth.verifyToken,
  UsersController.upgradeContentPremium
);

router.get(
  "/content-premium-plans/:user_id",
  Auth.verifyToken,
  UsersController.getContentPremiumPlans
);

router.post('/viewPaidVideo', Auth.verifyToken, ReelsController.viewPaidVideo);

// New: Subscription-based paid video view API
router.post('/viewSubscriptionPaidVideo', Auth.verifyToken, ReelsController.viewSubscriptionPaidVideo);

// New: Paid video view for owner without content creator premium
router.post('/viewPaidVideoNoPremium', Auth.verifyToken, ReelsController.viewPaidVideoNoPremium);

//27.1. watch normal video count api :
router.post('/viewNormalVideo', Auth.verifyToken, ReelsController.watchNormalVideo);

//28.ludo game api's
// Ludo game routes
router.post("/search-players", LudoController.searchPlayers);
router.post("/deduct-money-for-game", LudoController.deductMoneyForGame);
router.post("/add-money/:userId", LudoController.addMoney);
router.post("/ludo-game-history", LudoController.getGameHistory);

// New Ludo REST APIs
router.get("/ludo/rooms", LudoController.getRoomList);
router.post("/ludo/join", LudoController.joinRoom);
router.post("/ludo/continue", LudoController.continueGame);
router.post("/get-lobby-counts", Auth.verifyToken, LudoController.getLobbyCounts);

//29. explore and story api's:
// New explore route
router.post('/explore', Auth.verifyToken, exploreController);


//30. get names of users from user id :
// Get name of one user
router.post('/get-user-name', Auth.verifyToken,UsersController.getSingleUserName);

// Get names of multiple users
router.post('/get-multiple-user-names',Auth.verifyToken, UsersController.getMultipleUserNames);

// Search users by name with pagination
router.post('/search-users', Auth.verifyToken, UsersController.searchUsersByName);

// Search content (videos, posts, etc.)
router.post('/search-content', Auth.verifyToken, UsersController.searchContent);

//testing api's for users_table , orders_table ,transactions_table
// New routes for fetching data
router.get("/users/mobile/:mobile_number", Auth.verifyToken, UsersController.getUserByMobile);
router.get("/orders/user/:user_id", Auth.verifyToken, UsersController.getOrdersByUserId);
router.get("/transactions/user/:user_id", Auth.verifyToken, UsersController.getTransactionsByUserId);
router.post("/users/multiple", Auth.verifyToken, UsersController.getMultipleUsers);
router.post("/transactions/user/phone", Auth.verifyToken, UsersController.getTransactionsByPhoneNumber);

// New route to get user data by POST request
router.post("/get-user-data", Auth.verifyToken, UsersController.getUserByPost);

// Admin: Run cleanup of old chat messages (for testing only)
const { exec } = require('child_process');
router.post('/admin/cleanup-old-messages', (req, res) => {
  exec('node scripts/cleanupOldMessages.js', (err, stdout, stderr) => {
    if (err) return res.status(500).send({ status: 500, message: stderr });
    res.send({ status: 200, message: stdout });
  });
});

// Content Creator Premium Subscription Routes
router.get("/content-premium-plans", Auth.verifyToken, ContentCreatorSubscriptionController.getContentPremiumPlans);
// Get content creator premium plans with GST for Razorpay checkout
//router.get("/content-premium-plans-with-gst", Auth.verifyToken, ContentCreatorSubscriptionController.getContentPremiumPlansWithGST);
router.post("/content-premium/create", Auth.verifyToken, ContentCreatorSubscriptionController.createContentPremiumSubscription);
router.post("/content-premium/cancel", Auth.verifyToken, ContentCreatorSubscriptionController.cancelContentPremiumSubscription);
router.get("/content-premium/status/:userId", Auth.verifyToken, ContentCreatorSubscriptionController.getContentPremiumStatus);
router.get("/content-premium/razorpay-details", Auth.verifyToken, ContentCreatorSubscriptionController.getContentPremiumRazorpayDetails);
router.post("/content-premium/webhook", ContentCreatorSubscriptionController.handleContentPremiumWebhook);

// Legacy routes for backward compatibility
router.get('/content-creator-plans', Auth.verifyToken, ContentCreatorPlanController.getPlans);
router.post('/content-creator-order', Auth.verifyToken, ContentCreatorPlanController.createOrder);
router.get('/content-creator-plan-status/:userId', Auth.verifyToken, ContentCreatorPlanController.getUserPlanStatus);
router.post('/content-creator-payment-callback', ContentCreatorPlanController.handlePaymentCallback);

// Add new route for ad reward wallet crediting
router.post('/wallet/credit-ad-reward', Auth.verifyToken, WalletController.creditAdReward);

// Reward API routes
router.get('/reward/history', Auth.verifyToken, RewardController.getRewardHistory);
router.get('/reward/balance', Auth.verifyToken, RewardController.getRewardBalance);
router.get('/reward/summary', Auth.verifyToken, RewardController.getRewardSummary);

// 31. Promoted Post View API
const PromotedPostController = require('../controllers/PromotedPostController');
router.post('/view-promoted-post', Auth.verifyToken, PromotedPostController.handlePromotedPostView);

// 32. Contact Form APIs
router.post('/contact/submit', ContactController.submitContactForm); // Public endpoint (no auth required)
router.get('/contact/submissions', Auth.verifyToken, ContactController.getContactSubmissions); // Admin only
router.get('/contact/submissions/:id', Auth.verifyToken, ContactController.getContactSubmissionById); // Admin only
router.put('/contact/submissions/:id/status', Auth.verifyToken, ContactController.updateContactSubmissionStatus); // Admin only
router.post('/contact/submissions/:id/response', Auth.verifyToken, ContactController.addContactResponse); // Admin only

//33. manual admin/testing routes:
// --- MANUAL ADMIN/TESTING ROUTES ---
router.post('/manual/addfunds', Auth.verifyToken, ManualWalletController.manualAddFunds);
router.post('/manual/user-premium/create', Auth.verifyToken, ManualUserPremiumController.manualCreateUserPremium);
router.post('/manual/user-premium/cancel', Auth.verifyToken, ManualUserPremiumController.manualCancelUserPremium);
router.post('/manual/content-premium/create', Auth.verifyToken, ManualContentPremiumController.manualCreateContentPremium);
router.post('/manual/content-premium/cancel', Auth.verifyToken, ManualContentPremiumController.manualCancelContentPremium);

module.exports = { router, app };
