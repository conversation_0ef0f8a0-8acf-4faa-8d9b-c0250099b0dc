# 🚀 MySQL 8 Workbench Deployment Plan

## 📋 **Pre-Deployment Checklist**

### ✅ **Prerequisites**
- [ ] MySQL 8.0+ server running
- [ ] MySQL Workbench installed and connected
- [ ] Database backup completed (if upgrading existing system)
- [ ] Sufficient database privileges (CREATE, ALTER, INSERT, UPDATE, DELETE)
- [ ] Existing `users` table (required for foreign keys and triggers)

### ✅ **Files Required**
- [ ] `mysql8_workbench_deployment.sql` - Main deployment script
- [ ] `migrate_to_user_based_chats.sql` - Migration script (if upgrading)

## 🔧 **Step-by-Step Deployment**

### **Step 1: Database Preparation**
```sql
-- 1.1 Connect to your database in MySQL Workbench
USE your_database_name;

-- 1.2 Check if users table exists (required for foreign keys)
SHOW TABLES LIKE 'users';

-- 1.3 Backup existing data (if any)
-- Run this only if you have existing conversation-based data
CREATE TABLE messages_backup_20250123 AS SELECT * FROM messages WHERE id > 0;
CREATE TABLE conversations_backup_20250123 AS SELECT * FROM conversations WHERE id > 0;
```

### **Step 2: Execute Deployment Script Sections**

#### **Section 1: Cleanup (Run First)**
```sql
-- Copy and paste this section from mysql8_workbench_deployment.sql
-- STEP 1: DROP EXISTING TRIGGERS AND FUNCTIONS (IF ANY)
DROP TRIGGER IF EXISTS update_user_chat_metadata_on_message;
DROP TRIGGER IF EXISTS create_delivery_status_on_message;
-- ... (rest of cleanup section)
```

#### **Section 2: Create Tables**
```sql
-- Copy and paste this section from mysql8_workbench_deployment.sql
-- STEP 2: CREATE MAIN TABLES
CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    chat_id VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2 (sorted)',
    -- ... (rest of table creation)
```

#### **Section 3: Create Indexes**
```sql
-- Copy and paste this section from mysql8_workbench_deployment.sql
-- STEP 3: CREATE INDEXES
ALTER TABLE messages 
ADD INDEX idx_chat_id_created (chat_id, created_at DESC),
-- ... (rest of indexes)
```

#### **Section 4: Foreign Keys (Optional)**
```sql
-- Only run if you have users table and want referential integrity
-- Uncomment the foreign key section in the deployment script
-- STEP 4: ADD FOREIGN KEY CONSTRAINTS (OPTIONAL)
```

#### **Section 5: Functions and Procedures**
```sql
-- Copy and paste these sections one by one
-- STEP 5: CREATE HELPER FUNCTION
DELIMITER $$
CREATE FUNCTION GenerateChatId(user_id_1 INT, user_id_2 INT) 
-- ... (rest of function)

-- STEP 6: CREATE STORED PROCEDURES
DELIMITER $$
CREATE PROCEDURE MarkMessagesAsRead(
-- ... (rest of procedures)
```

#### **Section 6: Triggers (Optional)**
```sql
-- Only run if you have users table
-- Uncomment the trigger section in the deployment script
-- STEP 7: CREATE TRIGGERS (OPTIONAL)
```

#### **Section 7: Views**
```sql
-- Copy and paste this section
-- STEP 8: CREATE VIEWS
CREATE VIEW user_chat_list AS
-- ... (rest of views)
```

#### **Section 8: Verification**
```sql
-- Copy and paste this section
-- STEP 9: VERIFICATION QUERIES
SELECT 'Tables created successfully' as status;
SHOW TABLES LIKE '%message%';
-- ... (rest of verification)
```

## 🔍 **Troubleshooting Common Issues**

### **Error: Unknown column 'chat_id' in 'NEW'**
**Solution**: This was fixed in the updated script. The trigger now properly references `NEW.chat_id`.

### **Error: Table 'users' doesn't exist**
**Solution**: 
1. Comment out foreign key constraints section
2. Comment out triggers section
3. Create a dummy users table or modify references

```sql
-- Temporary users table if needed
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    username VARCHAR(255),
    profile_image VARCHAR(500),
    is_online BOOLEAN DEFAULT FALSE
);
```

### **Error: Function already exists**
**Solution**: Run the cleanup section first to drop existing objects.

### **Error: Syntax error near DELIMITER**
**Solution**: In MySQL Workbench, execute DELIMITER statements separately or use the script execution feature.

## 📊 **Post-Deployment Verification**

### **1. Verify Tables Created**
```sql
-- Check all tables exist
SHOW TABLES LIKE '%message%';
SHOW TABLES LIKE '%user_chat%';

-- Check table structure
DESCRIBE messages;
DESCRIBE user_chat_metadata;
DESCRIBE messages_local_sync;
DESCRIBE message_delivery_status;
```

### **2. Verify Indexes**
```sql
-- Check indexes on messages table
SHOW INDEX FROM messages;

-- Check performance
EXPLAIN SELECT * FROM messages WHERE chat_id = 'chat_1_2' ORDER BY created_at DESC LIMIT 50;
```

### **3. Verify Functions and Procedures**
```sql
-- Test chat ID generation
SELECT GenerateChatId(1, 2) as chat_id;
SELECT GenerateChatId(2, 1) as chat_id; -- Should return same result

-- Check procedures exist
SHOW PROCEDURE STATUS WHERE Name IN ('MarkMessagesAsRead', 'GetChatMessages');
```

### **4. Test Basic Operations**
```sql
-- Test message insertion (replace with real user IDs)
INSERT INTO messages (chat_id, sender_id, recipient_id, sender_name, content) 
VALUES ('chat_1_2', 1, 2, 'Test User', 'Hello World!');

-- Test chat metadata creation
INSERT INTO user_chat_metadata (user_id, chat_id, other_user_id, other_user_name)
VALUES (1, 'chat_1_2', 2, 'Other User');

-- Test message retrieval
SELECT * FROM messages WHERE chat_id = 'chat_1_2';
```

## 🔄 **Migration from Conversation-Based System**

### **If you have existing conversation-based data:**

1. **Run the migration script**:
```sql
-- Execute migrate_to_user_based_chats.sql
-- This will convert existing conversations to user-based chats
```

2. **Verify migration**:
```sql
-- Check migration results
SELECT 'Original Messages' as metric, COUNT(*) as count FROM messages WHERE conversation_id IS NOT NULL;
SELECT 'Migrated Messages' as metric, COUNT(*) as count FROM messages WHERE chat_id IS NOT NULL;
```

## 🚀 **Backend API Deployment**

### **1. Update Environment Variables**
```bash
# Add to your .env file
DB_CHAT_SYSTEM=user_based
MESSAGE_RETENTION_DAYS=7
CLEANUP_ENABLED=true
```

### **2. Install Dependencies**
```bash
cd adtipback
npm install
```

### **3. Test API Endpoints**
```bash
# Test the new user-based endpoints
curl -X GET "http://localhost:3000/api/chat/test"
curl -X GET "http://localhost:3000/api/chat/user-chats" -H "Authorization: Bearer YOUR_TOKEN"
```

## 📱 **Frontend Deployment**

### **1. Update React Native App**
```bash
cd adtip-reactnative/Adtip
npm install
```

### **2. Test WatermelonDB Integration**
- Verify local message storage works
- Test background FCM message handling
- Confirm sync functionality

## 🔧 **Setup Message Cleanup**

### **1. Configure Cron Jobs**
```bash
# Run the setup script
sudo ./adtipback/scripts/setup-message-cleanup.sh --environment prod --retention-days 7 --install-cron
```

### **2. Test Cleanup Script**
```bash
# Test cleanup script
node adtipback/scripts/cleanup-old-messages.js --dry-run --verbose
```

## 📈 **Monitoring and Maintenance**

### **1. Monitor Database Performance**
```sql
-- Check table sizes
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name LIKE '%message%';
```

### **2. Monitor Cleanup Effectiveness**
```bash
# Check cleanup logs
tail -f /var/log/adtip/message-cleanup.log

# Check cleanup statistics
node adtipback/scripts/cleanup-old-messages.js --stats-only
```

## ✅ **Deployment Checklist**

- [ ] Database schema deployed successfully
- [ ] All tables created with proper indexes
- [ ] Functions and procedures working
- [ ] Foreign keys added (if applicable)
- [ ] Triggers enabled (if applicable)
- [ ] Views created successfully
- [ ] Backend API updated and tested
- [ ] Frontend app updated and tested
- [ ] Message cleanup configured
- [ ] Monitoring setup complete
- [ ] Documentation updated

## 🆘 **Rollback Plan**

### **If deployment fails:**

1. **Restore from backup**:
```sql
-- Restore original tables
DROP TABLE IF EXISTS messages;
CREATE TABLE messages AS SELECT * FROM messages_backup_20250123;
```

2. **Revert API changes**:
```bash
git checkout previous_version
npm install
```

3. **Contact support** with error logs and deployment details.

---

## 📞 **Support**

If you encounter issues during deployment:
1. Check the troubleshooting section above
2. Verify all prerequisites are met
3. Review error logs carefully
4. Test each section individually

The deployment script is designed to be MySQL 8 Workbench compatible and should resolve the `Unknown column 'chat_id'` error you encountered.
