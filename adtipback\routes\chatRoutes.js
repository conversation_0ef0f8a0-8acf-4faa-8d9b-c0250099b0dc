/**
 * User-Based Chat Routes
 *
 * These routes handle REST API endpoints for the user-based chat system
 * using direct user-to-user messaging without conversation tables.
 *
 * Includes FCM high priority notification endpoints for reliable real-time message delivery.
 * Uses chat_userId1_userId2 format for direct user mapping.
 */

const express = require('express');
const router = express.Router();
const auth = require('../config/auth');
const queryRunner = require('../dbConfig/queryRunner');
const UserBasedChatService = require('../services/UserBasedChatService');
const FCMMessageQueueService = require('../services/FCMMessageQueueService');
const FCMService = require('../utils/fcm');

// Helper function to generate chat_id from user IDs
function generateChatId(userId1, userId2) {
  const [user1, user2] = [parseInt(userId1), parseInt(userId2)].sort((a, b) => a - b);
  return `chat_${user1}_${user2}`;
}

/**
 * Test FCM notification endpoint
 * POST /api/chat/test-fcm
 */
router.post('/test-fcm', async (req, res) => {
  try {
    const { token, title, body } = req.body;

    if (!token) {
      return res.status(400).json({
        status: 400,
        message: 'FCM token is required'
      });
    }

    const FCMService = require('../utils/fcm');

    const notificationData = {
      token: token,
      title: title || 'Test Chat Notification',
      body: body || 'This is a test message from the new chat system!',
      type: 'chat_message',
      chatId: 'chat_1_2',
      senderId: '1',
      senderName: 'Test User',
      timestamp: new Date().toISOString()
    };

    const result = await FCMService.sendChatNotification(
      token,
      notificationData.title,
      notificationData.body,
      notificationData.chatId,
      notificationData.senderId
    );

    res.json({
      status: 200,
      message: 'FCM test notification sent',
      data: result
    });

  } catch (error) {
    console.error('Error sending test FCM notification:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to send test FCM notification',
      error: error.message
    });
  }
});

/**
 * Test endpoint to verify chat system is working
 * GET /api/chat/test
 */
router.get('/test', async (req, res) => {
  try {
    // Test database connectivity for user-based system
    const [messages] = await queryRunner.queryRunner('SELECT COUNT(*) as count FROM messages WHERE chat_id IS NOT NULL', []);
    const [userChats] = await queryRunner.queryRunner('SELECT COUNT(*) as count FROM user_chat_metadata', []);
    const [syncEntries] = await queryRunner.queryRunner('SELECT COUNT(*) as count FROM messages_local_sync', []);

    // Test FCM message queue
    const queueStats = FCMMessageQueueService.getQueueStats();

    res.json({
      status: 200,
      message: 'User-based chat system is working correctly',
      data: {
        database: {
          messages: messages.count,
          userChats: userChats.count,
          syncEntries: syncEntries.count
        },
        fcmQueue: {
          pendingMessages: queueStats.pendingMessages,
          processedMessages: queueStats.processedMessages,
          failedMessages: queueStats.failedMessages
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in chat test endpoint:', error);
    res.status(500).json({
      status: 500,
      message: 'Chat system test failed',
      error: error.message
    });
  }
});

/**
 * Get user's chats with last message and unread count (user-based)
 * GET /api/chat/user-chats
 */
router.get('/user-chats', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Get user chats with metadata
    const chatsQuery = `
      SELECT
        ucm.*,
        u.name as other_user_display_name,
        u.profile_image as other_user_profile_image,
        u.is_online as other_user_online_status
      FROM user_chat_metadata ucm
      JOIN users u ON ucm.other_user_id = u.id
      WHERE ucm.user_id = ? AND ucm.is_archived = FALSE
      ORDER BY ucm.last_activity_at DESC
      LIMIT ? OFFSET ?
    `;

    const chats = await queryRunner.queryRunner(chatsQuery, [userId, limit, offset]);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_chat_metadata ucm
      WHERE ucm.user_id = ? AND ucm.is_archived = FALSE
    `;
    const [countResult] = await queryRunner.queryRunner(countQuery, [userId]);

    const totalChats = countResult.total;
    const totalPages = Math.ceil(totalChats / limit);

    res.json({
      status: 200,
      message: 'User chats retrieved successfully',
      data: chats,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: totalChats,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching user chats:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to fetch user chats',
      error: error.message
    });
  }
});

/**
 * Get messages for a specific chat (user-based) with incremental sync support
 * GET /api/chat/messages/:chatId?since_timestamp=2024-01-01T00:00:00.000Z
 */
router.get('/messages/:chatId', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const chatId = req.params.chatId;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;
    const sinceTimestamp = req.query.since_timestamp;

    // Verify user is participant in this chat
    const chatParts = chatId.split('_');
    if (chatParts.length !== 3 || chatParts[0] !== 'chat') {
      return res.status(400).json({
        status: 400,
        message: 'Invalid chat ID format. Expected: chat_userId1_userId2'
      });
    }

    const [, userId1, userId2] = chatParts;
    if (userId.toString() !== userId1 && userId.toString() !== userId2) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized to access this chat'
      });
    }

    // Build query with optional timestamp filtering for incremental sync
    let messagesQuery = `
      SELECT
        m.*,
        mds.status as delivery_status,
        mds.timestamp as delivery_timestamp
      FROM messages m
      LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
      WHERE m.chat_id = ? AND m.is_deleted = FALSE
    `;

    const queryParams = [userId, chatId];

    // Add timestamp filter for incremental sync
    if (sinceTimestamp) {
      messagesQuery += ` AND m.created_at > ?`;
      queryParams.push(sinceTimestamp);
      console.log(`[ChatRoutes] Using incremental sync since: ${sinceTimestamp}`);
    } else {
      console.log(`[ChatRoutes] Using full sync (no timestamp filter)`);
    }

    messagesQuery += ` ORDER BY m.created_at DESC, m.id DESC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    const messages = await queryRunner.queryRunner(messagesQuery, queryParams);

    // Get total count for pagination (with same timestamp filter)
    let countQuery = `
      SELECT COUNT(*) as total
      FROM messages m
      WHERE m.chat_id = ? AND m.is_deleted = FALSE
    `;

    const countParams = [chatId];

    if (sinceTimestamp) {
      countQuery += ` AND m.created_at > ?`;
      countParams.push(sinceTimestamp);
    }

    const [countResult] = await queryRunner.queryRunner(countQuery, countParams);

    const totalMessages = countResult.total;
    const totalPages = Math.ceil(totalMessages / limit);

    res.json({
      status: 200,
      message: sinceTimestamp ?
        `Incremental sync completed: ${messages.length} new messages since ${sinceTimestamp}` :
        'Messages retrieved successfully',
      data: {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalMessages,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        },
        sync: {
          isIncremental: !!sinceTimestamp,
          sinceTimestamp: sinceTimestamp || null,
          newMessagesCount: messages.length
        }
      }
    });

  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to fetch messages',
      error: error.message
    });
  }
});

/**
 * Send a message to another user (user-based)
 * POST /api/chat/send-message
 */
router.post('/send-message', auth.verifyToken, async (req, res) => {
  try {
    const senderId = req.user.user_id;
    const { recipientId, content, messageType, replyToMessageId, tempId } = req.body;

    // Validate required fields
    if (!recipientId || !content) {
      return res.status(400).json({
        status: 400,
        message: 'Recipient ID and message content are required'
      });
    }

    // Verify recipient exists
    const recipientQuery = 'SELECT id, name, profile_image FROM users WHERE id = ?';
    const recipients = await queryRunner.queryRunner(recipientQuery, [recipientId]);

    if (recipients.length === 0) {
      return res.status(404).json({
        status: 404,
        message: 'Recipient user not found'
      });
    }

    const recipient = recipients[0];

    // Get sender information
    const senderQuery = 'SELECT id, name, profile_image FROM users WHERE id = ?';
    const senders = await queryRunner.queryRunner(senderQuery, [senderId]);
    const sender = senders[0];

    // Generate chat_id
    const chatId = generateChatId(senderId, recipientId);

    // Insert message into database
    const messageQuery = `
      INSERT INTO messages (
        chat_id, sender_id, recipient_id, sender_name, sender_avatar,
        content, message_type, reply_to_message_id, temp_id, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'sent')
    `;

    const messageValues = [
      chatId,
      senderId,
      recipientId,
      sender.name || 'Unknown User',
      sender.profile_image,
      content,
      messageType || 'text',
      replyToMessageId || null,
      tempId || null
    ];

    const result = await queryRunner.queryRunner(messageQuery, messageValues);
    const messageId = result.insertId;

    // Get the created message
    const createdMessageQuery = 'SELECT * FROM messages WHERE id = ?';
    const [createdMessage] = await queryRunner.queryRunner(createdMessageQuery, [messageId]);

    // Create sync tracking entry
    if (tempId) {
      const syncQuery = `
        INSERT INTO messages_local_sync (
          message_id, chat_id, local_message_id, local_timestamp,
          server_timestamp, sync_status
        ) VALUES (?, ?, ?, ?, ?, 'synced')
      `;
      await queryRunner.queryRunner(syncQuery, [
        messageId, chatId, tempId, new Date(), new Date()
      ]);
    }

    res.json({
      status: 200,
      message: 'Message sent successfully',
      data: {
        message: createdMessage,
        chatId: chatId
      }
    });

  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to send message',
      error: error.message
    });
  }
});

/**
 * Create or get a user chat (user-based)
 * POST /api/chat/create-or-get-chat
 */
router.post('/create-or-get-chat', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { otherUserId } = req.body;

    if (!otherUserId) {
      return res.status(400).json({
        status: 400,
        message: 'Other user ID is required'
      });
    }

    // Verify other user exists
    const userQuery = 'SELECT id, name, profile_image FROM users WHERE id = ?';
    const users = await queryRunner.queryRunner(userQuery, [otherUserId]);

    if (users.length === 0) {
      return res.status(404).json({
        status: 404,
        message: 'User not found'
      });
    }

    const otherUser = users[0];
    const currentUserData = await queryRunner.queryRunner(userQuery, [userId]);
    const currentUser = currentUserData[0];

    // Generate chat_id
    const chatId = generateChatId(userId, otherUserId);

    // Create or update user chat metadata for both users
    const metadataQuery = `
      INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name, other_user_avatar
      ) VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        other_user_name = VALUES(other_user_name),
        other_user_avatar = VALUES(other_user_avatar),
        updated_at = CURRENT_TIMESTAMP
    `;

    // Create metadata for current user
    await queryRunner.queryRunner(metadataQuery, [
      userId, chatId, otherUserId, otherUser.name, otherUser.profile_image
    ]);

    // Create metadata for other user
    await queryRunner.queryRunner(metadataQuery, [
      otherUserId, chatId, userId, currentUser.name, currentUser.profile_image
    ]);

    res.json({
      status: 200,
      message: 'Chat created or retrieved successfully',
      data: {
        chatId: chatId,
        otherUser: {
          id: otherUser.id,
          name: otherUser.name,
          profileImage: otherUser.profile_image
        }
      }
    });

  } catch (error) {
    console.error('Error creating/getting chat:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to create or get chat',
      error: error.message
    });
  }
});

/**
 * Mark messages as read in a chat
 * POST /api/chat/mark-read
 */
router.post('/mark-read', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { chatId, lastReadMessageId } = req.body;

    if (!chatId || !lastReadMessageId) {
      return res.status(400).json({
        status: 400,
        message: 'Chat ID and last read message ID are required'
      });
    }

    // Verify user is participant in this chat
    const chatParts = chatId.split('_');
    if (chatParts.length !== 3 || chatParts[0] !== 'chat') {
      return res.status(400).json({
        status: 400,
        message: 'Invalid chat ID format'
      });
    }

    const [, userId1, userId2] = chatParts;
    if (userId.toString() !== userId1 && userId.toString() !== userId2) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized to access this chat'
      });
    }

    // Call stored procedure to mark messages as read
    await queryRunner.queryRunner('CALL MarkMessagesAsRead(?, ?, ?)', [
      userId, chatId, lastReadMessageId
    ]);

    res.json({
      status: 200,
      message: 'Messages marked as read successfully'
    });

  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
});

/**
 * Sync messages from client to server
 * POST /api/chat/sync-messages
 */
router.post('/sync-messages', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { messages } = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({
        status: 400,
        message: 'Messages array is required'
      });
    }

    const syncResults = [];

    for (const messageData of messages) {
      try {
        const { tempId, chatId, recipientId, content, messageType, timestamp } = messageData;

        // Check if message already exists
        const existingQuery = 'SELECT id FROM messages WHERE temp_id = ? OR external_id = ?';
        const existing = await queryRunner.queryRunner(existingQuery, [tempId, tempId]);

        if (existing.length > 0) {
          syncResults.push({
            tempId,
            status: 'already_exists',
            serverId: existing[0].id
          });
          continue;
        }

        // Get sender info
        const senderQuery = 'SELECT name, profile_image FROM users WHERE id = ?';
        const [sender] = await queryRunner.queryRunner(senderQuery, [userId]);

        // Insert message
        const insertQuery = `
          INSERT INTO messages (
            chat_id, sender_id, recipient_id, sender_name, sender_avatar,
            content, message_type, temp_id, status, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'sent', ?)
        `;

        const result = await queryRunner.queryRunner(insertQuery, [
          chatId, userId, recipientId, sender.name, sender.profile_image,
          content, messageType || 'text', tempId, new Date(timestamp)
        ]);

        // Create sync tracking
        const syncQuery = `
          INSERT INTO messages_local_sync (
            message_id, chat_id, local_message_id, local_timestamp,
            server_timestamp, sync_status
          ) VALUES (?, ?, ?, ?, ?, 'synced')
        `;
        await queryRunner.queryRunner(syncQuery, [
          result.insertId, chatId, tempId, new Date(timestamp), new Date()
        ]);

        syncResults.push({
          tempId,
          status: 'synced',
          serverId: result.insertId
        });

      } catch (error) {
        console.error(`Error syncing message ${messageData.tempId}:`, error);
        syncResults.push({
          tempId: messageData.tempId,
          status: 'failed',
          error: error.message
        });
      }
    }

    res.json({
      status: 200,
      message: 'Messages sync completed',
      data: {
        syncResults,
        totalMessages: messages.length,
        successCount: syncResults.filter(r => r.status === 'synced').length,
        failureCount: syncResults.filter(r => r.status === 'failed').length
      }
    });

  } catch (error) {
    console.error('Error syncing messages:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to sync messages',
      error: error.message
    });
  }
});

/**
 * Create a new conversation (direct chat)
 * POST /api/chat/conversations
 */
router.post('/conversations', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { participantId, type = 'direct', title } = req.body;
    
    if (!participantId) {
      return res.status(400).json({
        status: 400,
        message: 'Participant ID is required'
      });
    }
    
    // Check if conversation already exists between these users
    const existingConversation = `
      SELECT c.id 
      FROM conversations c
      JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
      JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
      WHERE c.type = 'direct'
        AND cp1.user_id = ? AND cp1.left_at IS NULL
        AND cp2.user_id = ? AND cp2.left_at IS NULL
    `;
    
    const existing = await queryRunner.queryRunner(existingConversation, [userId, participantId]);
    
    if (existing && existing.length > 0) {
      return res.json({
        status: 200,
        message: 'Conversation already exists',
        data: { conversationId: existing[0].id }
      });
    }
    
    // Create new conversation
    const createConversation = `
      INSERT INTO conversations (type, title, created_by, created_at, last_activity_at)
      VALUES (?, ?, ?, NOW(), NOW())
    `;
    
    const conversationResult = await queryRunner.queryRunner(createConversation, [
      type, title, userId
    ]);
    
    const conversationId = conversationResult.insertId;
    
    // Add participants
    const addParticipants = `
      INSERT INTO conversation_participants (conversation_id, user_id, joined_at)
      VALUES (?, ?, NOW()), (?, ?, NOW())
    `;
    
    await queryRunner.queryRunner(addParticipants, [
      conversationId, userId, conversationId, participantId
    ]);
    
    res.json({
      status: 200,
      message: 'Conversation created successfully',
      data: { conversationId: conversationId }
    });
    
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to create conversation',
      error: error.message
    });
  }
});

/**
 * Mark messages as read
 * PUT /api/chat/conversations/:conversationId/read
 */
router.put('/conversations/:conversationId/read', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const conversationId = req.params.conversationId;
    const { messageId } = req.body;
    
    // Mark specific message as read or all messages in conversation
    let sql, params;
    
    if (messageId) {
      sql = `
        INSERT INTO message_status (message_id, user_id, status, timestamp)
        VALUES (?, ?, 'read', NOW())
        ON DUPLICATE KEY UPDATE timestamp = NOW()
      `;
      params = [messageId, userId];
    } else {
      // Mark all unread messages in conversation as read
      sql = `
        INSERT INTO message_status (message_id, user_id, status, timestamp)
        SELECT m.id, ?, 'read', NOW()
        FROM messages m
        LEFT JOIN message_status ms ON (
          m.id = ms.message_id 
          AND ms.user_id = ? 
          AND ms.status = 'read'
        )
        WHERE m.conversation_id = ? 
          AND m.sender_id != ?
          AND ms.id IS NULL
        ON DUPLICATE KEY UPDATE timestamp = NOW()
      `;
      params = [userId, userId, conversationId, userId];
    }
    
    await queryRunner.queryRunner(sql, params);
    
    // Reset unread count for user
    const resetUnreadCount = `
      UPDATE conversation_participants 
      SET unread_count = 0
      WHERE conversation_id = ? AND user_id = ?
    `;
    
    await queryRunner.queryRunner(resetUnreadCount, [conversationId, userId]);

    // DISABLED: Socket.IO notification (replaced with FCM notifications)
    /*
    if (messageId) {
      ChatSocketService.sendToConversation(conversationId, 'message_read', {
        messageId: messageId,
        readBy: userId,
        readAt: new Date().toISOString()
      });
    }
    */
    
    res.json({
      status: 200,
      message: 'Messages marked as read successfully'
    });
    
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
});

/**
 * Get online status of users
 * POST /api/chat/users/status
 */
router.post('/users/status', auth.verifyToken, async (req, res) => {
  try {
    const { userIds } = req.body;
    
    if (!userIds || !Array.isArray(userIds)) {
      return res.status(400).json({
        status: 400,
        message: 'User IDs array is required'
      });
    }
    
    const placeholders = userIds.map(() => '?').join(',');
    const sql = `
      SELECT 
        u.id,
        u.name,
        u.profile_image,
        COALESCE(up.status, 'offline') as status,
        up.last_seen_at
      FROM users u
      LEFT JOIN user_presence up ON u.id = up.user_id
      WHERE u.id IN (${placeholders})
    `;
    
    const users = await queryRunner.queryRunner(sql, userIds);
    
    // Note: Real-time online status is now handled via FCM presence updates
    // Users status is determined by last_seen_at timestamp in user_presence table
    
    res.json({
      status: 200,
      message: 'User status retrieved successfully',
      data: users
    });
    
  } catch (error) {
    console.error('Error fetching user status:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to fetch user status',
      error: error.message
    });
  }
});

// =====================================================
// FCM HIGH PRIORITY NOTIFICATION ENDPOINTS
// =====================================================
// These endpoints handle message delivery via FCM high priority notifications
// for reliable real-time messaging

/**
 * Send a message via FCM high priority notification
 * POST /api/chat/fcm/send-message
 */
router.post('/fcm/send-message', auth.verifyToken, async (req, res) => {
  try {
    const senderId = req.user.user_id;
    const { conversationId, content, messageType, replyToMessageId, recipientId } = req.body;

    // Validate required fields
    if (!conversationId || !content) {
      return res.status(400).json({
        status: 400,
        message: 'Conversation ID and content are required'
      });
    }

    // Verify sender is participant in conversation
    const isParticipant = await NewChatService.verifyConversationParticipant(senderId, conversationId);
    if (!isParticipant) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized to send messages in this conversation'
      });
    }

    // Save message to database first
    const messageData = {
      content,
      messageType: messageType || 'text',
      replyToMessageId
    };

    const savedMessage = await NewChatService.sendMessage(senderId, conversationId, messageData);

    // Get recipient information if not provided
    let targetRecipientId = recipientId;
    if (!targetRecipientId) {
      // Get other participants in the conversation
      const participants = await queryRunner.queryRunner(
        `SELECT user_id FROM conversation_participants
         WHERE conversation_id = ? AND user_id != ? AND left_at IS NULL`,
        [conversationId, senderId]
      );

      if (participants.length === 0) {
        return res.status(400).json({
          status: 400,
          message: 'No recipients found in conversation'
        });
      }

      // For direct chats, use the other participant
      targetRecipientId = participants[0].user_id;
    }

    // Get sender information for notification
    const senderInfo = await queryRunner.queryRunner(
      'SELECT name, profile_image FROM users WHERE id = ?',
      [senderId]
    );

    const senderName = senderInfo.length > 0 ? senderInfo[0].name : 'Unknown User';

    // Queue message for FCM delivery
    const queueResult = await FCMMessageQueueService.queueChatMessage({
      messageId: savedMessage.id,
      conversationId,
      senderId,
      recipientId: targetRecipientId,
      content,
      senderName
    });

    if (!queueResult.success) {
      console.error('Failed to queue FCM message:', queueResult);
    }

    res.json({
      status: 200,
      message: 'Message sent successfully via FCM',
      data: {
        message: savedMessage,
        fcmQueued: queueResult.success,
        queueId: queueResult.queueId
      }
    });

  } catch (error) {
    console.error('Error sending FCM message:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to send message via FCM',
      error: error.message
    });
  }
});

/**
 * Send message to multiple recipients via FCM
 * POST /api/chat/fcm/send-message-multicast
 */
router.post('/fcm/send-message-multicast', auth.verifyToken, async (req, res) => {
  try {
    const senderId = req.user.user_id;
    const { conversationId, content, messageType, replyToMessageId, recipientIds } = req.body;

    // Validate required fields
    if (!conversationId || !content || !recipientIds || !Array.isArray(recipientIds)) {
      return res.status(400).json({
        status: 400,
        message: 'Conversation ID, content, and recipient IDs array are required'
      });
    }

    // Verify sender is participant in conversation
    const isParticipant = await NewChatService.verifyConversationParticipant(senderId, conversationId);
    if (!isParticipant) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized to send messages in this conversation'
      });
    }

    // Save message to database first
    const messageData = {
      content,
      messageType: messageType || 'text',
      replyToMessageId
    };

    const savedMessage = await NewChatService.sendMessage(senderId, conversationId, messageData);

    // Get sender information for notification
    const senderInfo = await queryRunner.queryRunner(
      'SELECT name, profile_image FROM users WHERE id = ?',
      [senderId]
    );

    const senderName = senderInfo.length > 0 ? senderInfo[0].name : 'Unknown User';

    // Queue message for each recipient
    const queueResults = [];
    for (const recipientId of recipientIds) {
      try {
        const queueResult = await FCMMessageQueueService.queueChatMessage({
          messageId: savedMessage.id,
          conversationId,
          senderId,
          recipientId,
          content,
          senderName
        });
        queueResults.push({ recipientId, success: queueResult.success, queueId: queueResult.queueId });
      } catch (error) {
        console.error(`Failed to queue FCM message for recipient ${recipientId}:`, error);
        queueResults.push({ recipientId, success: false, error: error.message });
      }
    }

    const successCount = queueResults.filter(r => r.success).length;

    res.json({
      status: 200,
      message: `Message sent to ${successCount}/${recipientIds.length} recipients via FCM`,
      data: {
        message: savedMessage,
        queueResults,
        successCount,
        totalRecipients: recipientIds.length
      }
    });

  } catch (error) {
    console.error('Error sending multicast FCM message:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to send multicast message via FCM',
      error: error.message
    });
  }
});

/**
 * Get FCM message queue statistics
 * GET /api/chat/fcm/queue-stats
 */
router.get('/fcm/queue-stats', auth.verifyToken, async (req, res) => {
  try {
    const stats = FCMMessageQueueService.getQueueStats();

    // Get additional database stats
    const deliveryStats = await queryRunner.queryRunner(`
      SELECT
        delivery_status,
        COUNT(*) as count
      FROM messages
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      GROUP BY delivery_status
    `);

    const failureStats = await queryRunner.queryRunner(`
      SELECT
        failure_reason,
        COUNT(*) as count
      FROM message_delivery_failures
      WHERE failed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      GROUP BY failure_reason
    `);

    res.json({
      status: 200,
      message: 'FCM queue statistics retrieved successfully',
      data: {
        queue: stats,
        last24Hours: {
          deliveryStats,
          failureStats
        }
      }
    });

  } catch (error) {
    console.error('Error getting FCM queue stats:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to get FCM queue statistics',
      error: error.message
    });
  }
});

/**
 * Retry failed messages in dead letter queue
 * POST /api/chat/fcm/retry-failed
 */
router.post('/fcm/retry-failed', auth.verifyToken, async (req, res) => {
  try {
    // Only allow admin users to retry failed messages
    const userId = req.user.user_id;

    // Check if user is admin (you may want to implement proper admin check)
    const userInfo = await queryRunner.queryRunner(
      'SELECT role FROM users WHERE id = ?',
      [userId]
    );

    if (userInfo.length === 0 || userInfo[0].role !== 'admin') {
      return res.status(403).json({
        status: 403,
        message: 'Admin access required to retry failed messages'
      });
    }

    await FCMMessageQueueService.retryDeadLetterQueue();

    res.json({
      status: 200,
      message: 'Dead letter queue retry initiated successfully'
    });

  } catch (error) {
    console.error('Error retrying failed messages:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to retry failed messages',
      error: error.message
    });
  }
});

/**
 * Get message delivery status
 * GET /api/chat/fcm/message-status/:messageId
 */
router.get('/fcm/message-status/:messageId', auth.verifyToken, async (req, res) => {
  try {
    const messageId = req.params.messageId;
    const userId = req.user.user_id;

    // Get message details with delivery status
    const messageInfo = await queryRunner.queryRunner(`
      SELECT
        m.*,
        u.name as sender_name,
        mdf.failure_reason,
        mdf.attempts as failure_attempts,
        mdf.failed_at
      FROM messages m
      JOIN users u ON m.sender_id = u.id
      LEFT JOIN message_delivery_failures mdf ON m.id = mdf.message_id
      WHERE m.id = ?
    `, [messageId]);

    if (messageInfo.length === 0) {
      return res.status(404).json({
        status: 404,
        message: 'Message not found'
      });
    }

    const message = messageInfo[0];

    // Verify user has access to this message
    const hasAccess = await NewChatService.verifyConversationParticipant(userId, message.conversation_id);
    if (!hasAccess) {
      return res.status(403).json({
        status: 403,
        message: 'You do not have access to this message'
      });
    }

    res.json({
      status: 200,
      message: 'Message delivery status retrieved successfully',
      data: {
        messageId: message.id,
        conversationId: message.conversation_id,
        deliveryStatus: message.delivery_status,
        deliveryAttempts: message.delivery_attempts,
        lastDeliveryAttempt: message.last_delivery_attempt,
        deliveredAt: message.delivered_at,
        failureReason: message.failure_reason,
        failureAttempts: message.failure_attempts,
        failedAt: message.failed_at
      }
    });

  } catch (error) {
    console.error('Error getting message delivery status:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to get message delivery status',
      error: error.message
    });
  }
});

/**
 * Update user FCM token
 * POST /api/chat/fcm/update-token
 */
router.post('/fcm/update-token', auth.verifyToken, async (req, res) => {
  try {
    const userId = req.user.user_id;
    const { fcmToken, platform } = req.body;

    if (!fcmToken) {
      return res.status(400).json({
        status: 400,
        message: 'FCM token is required'
      });
    }

    // Update FCM token in database
    await queryRunner.queryRunner(
      `UPDATE users
       SET fcm_token = ?, fcm_token_platform = ?, fcm_token_updation_date = NOW()
       WHERE id = ?`,
      [fcmToken, platform || 'unknown', userId]
    );

    res.json({
      status: 200,
      message: 'FCM token updated successfully'
    });

  } catch (error) {
    console.error('Error updating FCM token:', error);
    res.status(500).json({
      status: 500,
      message: 'Failed to update FCM token',
      error: error.message
    });
  }
});

/**
 * Save chat message to database (for cloud function integration)
 * POST /api/chat/save-message
 */
router.post('/save-message', auth.verifyToken, async (req, res) => {
  try {
    const senderId = req.user.user_id;
    const {
      conversationId,
      content,
      messageType,
      replyToMessageId,
      messageId, // Optional: messageId from cloud function
      tempId,    // Optional: temporary ID from client
      timestamp  // Optional: client timestamp
    } = req.body;

    console.log('[save-message] Request data:', {
      senderId,
      conversationId,
      content: content?.substring(0, 50) + '...',
      messageType,
      messageId,
      tempId,
      timestamp
    });

    // Validate required fields
    if (!conversationId || !content) {
      return res.status(400).json({
        status: 400,
        message: 'Conversation ID and content are required'
      });
    }

    // Verify sender is participant in conversation
    const isParticipant = await NewChatService.verifyConversationParticipant(senderId, conversationId);
    if (!isParticipant) {
      return res.status(403).json({
        status: 403,
        message: 'You are not authorized to send messages in this conversation'
      });
    }

    // Save message to database
    const messageData = {
      content,
      messageType: messageType || 'text',
      replyToMessageId
    };

    const savedMessage = await NewChatService.sendMessage(senderId, conversationId, messageData);

    // Update message with cloud function messageId and temp_id if provided
    if ((messageId || tempId) && savedMessage.id) {
      try {
        const updateFields = [];
        const updateValues = [];

        if (messageId) {
          updateFields.push('fcm_message_id = ?');
          updateValues.push(messageId);
        }

        if (tempId) {
          updateFields.push('temp_id = ?');
          updateValues.push(tempId);
        }

        if (updateFields.length > 0) {
          updateValues.push(savedMessage.id);
          await queryRunner.queryRunner(
            `UPDATE messages SET ${updateFields.join(', ')} WHERE id = ?`,
            updateValues
          );

          if (messageId) savedMessage.fcm_message_id = messageId;
          if (tempId) savedMessage.temp_id = tempId;
        }

        // Record sync status
        if (tempId) {
          try {
            await queryRunner.queryRunner(
              'CALL MarkMessageSynced(?, ?, ?)',
              [savedMessage.id, tempId, conversationId]
            );
            console.log('[save-message] Marked message as synced:', savedMessage.id);
          } catch (syncError) {
            console.warn('[save-message] Failed to mark as synced:', syncError);
          }
        }

      } catch (updateError) {
        console.warn('[save-message] Failed to update message metadata:', updateError);
        // Don't fail the request for this
      }
    }

    console.log('[save-message] Message saved successfully:', {
      messageId: savedMessage.id,
      conversationId,
      senderId
    });

    res.json({
      status: 200,
      message: 'Message saved successfully',
      data: {
        message: savedMessage,
        messageId: savedMessage.id,
        conversationId: conversationId,
        tempId: tempId
      }
    });

  } catch (error) {
    console.error('Error saving message:', error);

    if (error.message === 'Sender is not a participant in this conversation') {
      return res.status(403).json({
        status: 403,
        message: error.message
      });
    }

    res.status(500).json({
      status: 500,
      message: 'Failed to save message',
      error: error.message
    });
  }
});

module.exports = router;
