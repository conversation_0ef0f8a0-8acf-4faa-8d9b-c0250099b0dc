PS C:\A1\adtip-reactnative\Adtip> npx tsc --noEmit --project .











C:\A1\adtip-reactnative\Adtip\src\screens\adPassbook\AdPassbookScreen.tsx
  19:8   warning  'LinearGradient' is defined but never used  @typescript-eslint/no-unused-vars
  54:12  warning  'error' is defined but never used           @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\adPassbook\CreateCampaignScreen.tsx
    1:27  warning  'useRef' is defined but never used                         @typescript-eslint/no-unused-vars
   10:3   warning  'Switch' is defined but never used                         @typescript-eslint/no-unused-vars
   37:8   warning  'Video' is defined but never used                          @typescript-eslint/no-unused-vars
   39:10  warning  'CampaignCreateRequest' is defined but never used          @typescript-eslint/no-unused-vars
   39:33  warning  'CampaignCreateResponse' is defined but never used         @typescript-eslint/no-unused-vars
  107:12  warning  'error' is defined but never used                          @typescript-eslint/no-unused-vars
  115:10  warning  'mediaUrl' is assigned a value but never used              @typescript-eslint/no-unused-vars
  156:10  warning  'isDraggingDuration' is assigned a value but never used    @typescript-eslint/no-unused-vars
  159:10  warning  'isDraggingPayPerView' is assigned a value but never used  @typescript-eslint/no-unused-vars
  486:9   warning  'uploadMedia' is assigned a value but never used           @typescript-eslint/no-unused-vars
  708:32  error    A `require()` style import is forbidden                    @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\screens\analytics\AnalyticsScreen.tsx
  48:5  warning  'contentCreatorPremiumData' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\auth\LoginScreen.tsx
   12:3   warning  'Alert' is defined but never used                    @typescript-eslint/no-unused-vars
   40:23  warning  'setCountryCode' is assigned a value but never used  @typescript-eslint/no-unused-vars
  142:19  error    A `require()` style import is forbidden              @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\screens\auth\OTPScreen.tsx
   26:29  warning  'isFirstTime' is assigned a value but never used                 @typescript-eslint/no-unused-vars
  141:13  error    `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\auth\OnboardingScreen.tsx
   44:29  error    Component definition is missing display name  react/display-name
   89:27  error    Component definition is missing display name  react/display-name
  134:26  error    Component definition is missing display name  react/display-name
  187:26  error    Component definition is missing display name  react/display-name
  245:29  error    Component definition is missing display name  react/display-name
  369:10  warning  'colors' is assigned a value but never used   @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\auth\UserDetailsScreen.tsx
  65:16  warning  'updateUserDetails' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\blocklist\BlockedUsersScreen.tsx
  153:18  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\channel\ChannelScreen.tsx
  93:16  warning  'contentCreatorPremiumLoading' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\channel\ChannelSettingsScreen.tsx
  36:14  warning  'contentCreatorPremiumLoading' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\channel\CreateChannelScreen.tsx
   71:11  warning  'User' is defined but never used                                 @typescript-eslint/no-unused-vars
   79:10  warning  'channelData' is assigned a value but never used                 @typescript-eslint/no-unused-vars
   84:19  warning  'isDarkMode' is assigned a value but never used                  @typescript-eslint/no-unused-vars
  281:56  error    `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\channel\EditChannelScreen.tsx
  19:24  warning  'X' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\channel\MyChannelScreen.tsx
   13:3   warning  'Platform' is defined but never used                                     @typescript-eslint/no-unused-vars
   26:8   warning  'Header' is defined but never used                                       @typescript-eslint/no-unused-vars
   31:3   warning  'ChannelInfo' is defined but never used                                  @typescript-eslint/no-unused-vars
   33:3   warning  'VideoListResponse' is defined but never used                            @typescript-eslint/no-unused-vars
   34:3   warning  'FollowUserRequest' is defined but never used                            @typescript-eslint/no-unused-vars
   79:10  warning  'analytics' is assigned a value but never used                           @typescript-eslint/no-unused-vars
  100:5   warning  'refreshContentCreatorPremiumStatus' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\content\CameraRecordingScreen.tsx
  13:3   warning  'Platform' is defined but never used               @typescript-eslint/no-unused-vars
  15:3   warning  'PermissionsAndroid' is defined but never used     @typescript-eslint/no-unused-vars
  21:3   warning  'useCameraDevices' is defined but never used       @typescript-eslint/no-unused-vars
  36:16  warning  'screenWidth' is assigned a value but never used   @typescript-eslint/no-unused-vars
  36:37  warning  'screenHeight' is assigned a value but never used  @typescript-eslint/no-unused-vars
  41:19  warning  'isDarkMode' is assigned a value but never used    @typescript-eslint/no-unused-vars
  43:29  warning  'aspectRatio' is assigned a value but never used   @typescript-eslint/no-unused-vars
  67:16  warning  'setZoom' is assigned a value but never used       @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\content\CreateContentModal.tsx
   22:3  warning  'interpolate' is defined but never used                 @typescript-eslint/no-unused-vars
   23:3  warning  'Extrapolate' is defined but never used                 @typescript-eslint/no-unused-vars
  186:9  warning  'handleStartStream' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\content\CreatePostScreen.tsx
   23:8   warning  'MaterialIcons' is defined but never used                                                                            @typescript-eslint/no-unused-vars
   33:8   warning  'ApiService' is defined but never used                                                                               @typescript-eslint/no-unused-vars
  304:5   error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
  327:15  error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
  714:36  warning  'isDarkMode' is defined but never used. Allowed unused args must match /^_/u                                         @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\content\SelectCategoryScreen.tsx
  36:3  error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment

C:\A1\adtip-reactnative\Adtip\src\screens\content\TipShortsUploadScreen.tsx
   22:30  warning  'ImagePickerResponse' is defined but never used      @typescript-eslint/no-unused-vars
   35:16  warning  'screenWidth' is assigned a value but never used     @typescript-eslint/no-unused-vars
   35:37  warning  'screenHeight' is assigned a value but never used    @typescript-eslint/no-unused-vars
   38:11  warning  'TipShotUploadRequest' is defined but never used     @typescript-eslint/no-unused-vars
   50:11  warning  'TipShotUploadResponse' is defined but never used    @typescript-eslint/no-unused-vars
  122:10  warning  'videoPreview' is assigned a value but never used    @typescript-eslint/no-unused-vars
  133:23  warning  'setIsRecording' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\content\TipTubeUploadScreen.tsx
  22:30  warning  'ImagePickerResponse' is defined but never used    @typescript-eslint/no-unused-vars
  34:16  warning  'screenWidth' is assigned a value but never used   @typescript-eslint/no-unused-vars
  37:11  warning  'TipTubeUploadRequest' is defined but never used   @typescript-eslint/no-unused-vars
  51:11  warning  'TipTubeUploadResponse' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\earnings\EarningsScreen.tsx
   15:23  warning  'TrendingUp' is defined but never used        @typescript-eslint/no-unused-vars
   15:35  warning  'Calendar' is defined but never used          @typescript-eslint/no-unused-vars
   15:45  warning  'Target' is defined but never used            @typescript-eslint/no-unused-vars
   15:53  warning  'Gift' is defined but never used              @typescript-eslint/no-unused-vars
  209:9   error    Unexpected lexical declaration in case block  no-case-declarations

C:\A1\adtip-reactnative\Adtip\src\screens\explore\ExploreScreen.tsx
   1:17  warning  'useState' is defined but never used                                     @typescript-eslint/no-unused-vars
   1:27  warning  'useEffect' is defined but never used                                    @typescript-eslint/no-unused-vars
  11:40  warning  'createKeyExtractor' is defined but never used                           @typescript-eslint/no-unused-vars
  11:60  warning  'createGridLayout' is defined but never used                             @typescript-eslint/no-unused-vars
  20:19  warning  'isDarkMode' is assigned a value but never used                          @typescript-eslint/no-unused-vars
  24:9   warning  'insets' is assigned a value but never used                              @typescript-eslint/no-unused-vars
  55:43  warning  'index' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\home\CommentScreen.tsx
   14:29  error    A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  200:14  warning  'error' is defined but never used        @typescript-eslint/no-unused-vars
  238:14  warning  'error' is defined but never used        @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\home\HomeScreen.tsx
    11:3   warning  'Platform' is defined but never used                                                                                 @typescript-eslint/no-unused-vars
    27:10  warning  'PlayCircle' is defined but never used                                                                               @typescript-eslint/no-unused-vars
    27:41  warning  'Share2' is defined but never used                                                                                   @typescript-eslint/no-unused-vars
    27:49  warning  'HandCoins' is defined but never used                                                                                @typescript-eslint/no-unused-vars
    29:24  warning  'useOptimizedRenderItem' is defined but never used                                                                   @typescript-eslint/no-unused-vars
    36:8   warning  'ApiService' is defined but never used                                                                               @typescript-eslint/no-unused-vars
    38:9   warning  'HOME_ENDPOINTS' is defined but never used                                                                           @typescript-eslint/no-unused-vars
    42:10  warning  'getUserDisplayName' is defined but never used                                                                       @typescript-eslint/no-unused-vars
    42:30  warning  'isPremiumUser' is defined but never used                                                                            @typescript-eslint/no-unused-vars
    46:8   warning  'VersionCheckService' is defined but never used                                                                      @typescript-eslint/no-unused-vars
    55:8   warning  'EarnCard' is defined but never used                                                                                 @typescript-eslint/no-unused-vars
    77:10  warning  'generateVideoDebugReport' is defined but never used                                                                 @typescript-eslint/no-unused-vars
   203:66  warning  'isPremium' is defined but never used. Allowed unused args must match /^_/u                                          @typescript-eslint/no-unused-vars
   203:77  warning  'onUpgrade' is defined but never used. Allowed unused args must match /^_/u                                          @typescript-eslint/no-unused-vars
   266:68  warning  'isPremium' is defined but never used. Allowed unused args must match /^_/u                                          @typescript-eslint/no-unused-vars
   266:79  warning  'onUpgrade' is defined but never used. Allowed unused args must match /^_/u                                          @typescript-eslint/no-unused-vars
   323:54  warning  'onWatchAndEarn' is defined but never used. Allowed unused args must match /^_/u                                     @typescript-eslint/no-unused-vars
   393:10  warning  'shuffleArray' is defined but never used                                                                             @typescript-eslint/no-unused-vars
   405:25  warning  'refreshUserData' is assigned a value but never used                                                                 @typescript-eslint/no-unused-vars
   408:22  warning  'invalidateData' is assigned a value but never used                                                                  @typescript-eslint/no-unused-vars
   413:11  warning  'userData' is assigned a value but never used                                                                        @typescript-eslint/no-unused-vars
   413:32  warning  'userDataLoading' is assigned a value but never used                                                                 @typescript-eslint/no-unused-vars
   414:22  warning  'isPremiumNew' is assigned a value but never used                                                                    @typescript-eslint/no-unused-vars
   414:36  warning  'premiumExpiresAt' is assigned a value but never used                                                                @typescript-eslint/no-unused-vars
   415:26  warning  'userWalletBalance' is assigned a value but never used                                                               @typescript-eslint/no-unused-vars
   429:12  warning  'categoriesError' is assigned a value but never used                                                                 @typescript-eslint/no-unused-vars
   447:10  warning  'walletAmount' is assigned a value but never used                                                                    @typescript-eslint/no-unused-vars
   453:10  warning  'offerwallLoading' is assigned a value but never used                                                                @typescript-eslint/no-unused-vars
   476:10  warning  'banners' is assigned a value but never used                                                                         @typescript-eslint/no-unused-vars
   476:19  warning  'setBanners' is assigned a value but never used                                                                      @typescript-eslint/no-unused-vars
   477:10  warning  'bannersLoading' is assigned a value but never used                                                                  @typescript-eslint/no-unused-vars
   477:26  warning  'setBannersLoading' is assigned a value but never used                                                               @typescript-eslint/no-unused-vars
   675:9   warning  'handlePostPress' is assigned a value but never used                                                                 @typescript-eslint/no-unused-vars
   681:9   warning  'handleRetry' is assigned a value but never used                                                                     @typescript-eslint/no-unused-vars
   692:9   warning  'renderPremiumBanner' is assigned a value but never used                                                             @typescript-eslint/no-unused-vars
   740:9   warning  'getFullImageUrl' is assigned a value but never used                                                                 @typescript-eslint/no-unused-vars
   939:32  error    `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                      react/no-unescaped-entities
   939:55  error    `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                      react/no-unescaped-entities
  1080:14  warning  'err' is defined but never used                                                                                      @typescript-eslint/no-unused-vars
  1268:13  error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment

C:\A1\adtip-reactnative\Adtip\src\screens\installToEarn\InstallToEarnScreen.tsx
  149:46  error    `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`               react/no-unescaped-entities
  177:23  warning  'colors' is defined but never used. Allowed unused args must match /^_/u      @typescript-eslint/no-unused-vars
  177:36  warning  'isDarkMode' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\media\VideoPreviewScreen.tsx
  26:3  error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment

C:\A1\adtip-reactnative\Adtip\src\screens\media\VideoScreen.tsx
  90:3  error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment

C:\A1\adtip-reactnative\Adtip\src\screens\notifications\NotificationScreen.tsx
    1:16  warning  'useState' is defined but never used                             @typescript-eslint/no-unused-vars
   13:10  warning  'IndianRupee' is defined but never used                          @typescript-eslint/no-unused-vars
  264:16  error    `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\packages\CheckoutScreen.tsx
   9:3   warning  'TextInput' is defined but never used               @typescript-eslint/no-unused-vars
  16:8   warning  'MaterialCommunityIcons' is defined but never used  @typescript-eslint/no-unused-vars
  40:9   warning  'offers' is assigned a value but never used         @typescript-eslint/no-unused-vars
  49:13  error    A `require()` style import is forbidden             @typescript-eslint/no-require-imports
  55:13  error    A `require()` style import is forbidden             @typescript-eslint/no-require-imports
  61:13  error    A `require()` style import is forbidden             @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\screens\packages\ChoosePackagesScreen.tsx
   73:5   error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
  222:17  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                      react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\packages\ContentCreatorSubscriptionScreen.tsx
   11:3   warning  'Platform' is defined but never used                                                                                 @typescript-eslint/no-unused-vars
   13:3   warning  'StatusBar' is defined but never used                                                                                @typescript-eslint/no-unused-vars
   18:1   error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
   25:9   warning  'width' is assigned a value but never used                                                                           @typescript-eslint/no-unused-vars
   25:16  warning  'height' is assigned a value but never used                                                                          @typescript-eslint/no-unused-vars
   28:19  warning  'isDarkMode' is assigned a value but never used                                                                      @typescript-eslint/no-unused-vars
   55:16  warning  'error' is defined but never used                                                                                    @typescript-eslint/no-unused-vars
  112:16  warning  'data' is defined but never used. Allowed unused args must match /^_/u                                               @typescript-eslint/no-unused-vars
  138:34  warning  'index' is defined but never used. Allowed unused args must match /^_/u                                              @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\packages\PackagesScreen.tsx
  21:19  warning  'setLoading' is assigned a value but never used           @typescript-eslint/no-unused-vars
  29:9   warning  'handleSelectPackage' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\packages\SubscriptionScreen.tsx
   11:3   warning  'Platform' is defined but never used                                                                                 @typescript-eslint/no-unused-vars
   13:3   warning  'StatusBar' is defined but never used                                                                                @typescript-eslint/no-unused-vars
   18:1   error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
   25:9   warning  'width' is assigned a value but never used                                                                           @typescript-eslint/no-unused-vars
   25:16  warning  'height' is assigned a value but never used                                                                          @typescript-eslint/no-unused-vars
   28:19  warning  'isDarkMode' is assigned a value but never used                                                                      @typescript-eslint/no-unused-vars
   60:16  warning  'error' is defined but never used                                                                                    @typescript-eslint/no-unused-vars
  117:16  warning  'data' is defined but never used. Allowed unused args must match /^_/u                                               @typescript-eslint/no-unused-vars
  143:34  warning  'index' is defined but never used. Allowed unused args must match /^_/u                                              @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\playtoEarn\ChooseGamesScreen.tsx
  14:46  warning  'navigation' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\playtoEarn\LudoGameScreen.tsx
   11:3   warning  'Image' is defined but never used                          @typescript-eslint/no-unused-vars
   13:3   warning  'Modal' is defined but never used                          @typescript-eslint/no-unused-vars
   15:3   warning  'Platform' is defined but never used                       @typescript-eslint/no-unused-vars
   30:7   warning  'isSmallScreen' is assigned a value but never used         @typescript-eslint/no-unused-vars
  106:7   warning  'MOCK_USER_ID' is assigned a value but never used          @typescript-eslint/no-unused-vars
  153:7   warning  'contentPadding' is never reassigned. Use 'const' instead  prefer-const
  155:18  warning  'setUserId' is assigned a value but never used             @typescript-eslint/no-unused-vars
  169:10  warning  'serverMessageLog' is assigned a value but never used      @typescript-eslint/no-unused-vars
  170:21  warning  'setGameStats' is assigned a value but never used          @typescript-eslint/no-unused-vars
  184:10  warning  'retryCount' is assigned a value but never used            @typescript-eslint/no-unused-vars
  469:9   warning  'parseTokenId' is assigned a value but never used          @typescript-eslint/no-unused-vars
  625:9   warning  'finalPositionType' is assigned a value but never used     @typescript-eslint/no-unused-vars
  633:9   warning  'cutInfo' is never reassigned. Use 'const' instead         prefer-const

C:\A1\adtip-reactnative\Adtip\src\screens\premium\PremiumUserScreen.tsx
  11:3   warning  'Platform' is defined but never used         @typescript-eslint/no-unused-vars
  23:9   warning  'width' is assigned a value but never used   @typescript-eslint/no-unused-vars
  23:16  warning  'height' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\profile\EditProfile.tsx
   26:8   warning  'CloudflareUploadService' is defined but never used              @typescript-eslint/no-unused-vars
   27:10  warning  'handleProfileImageUploadResult' is defined but never used       @typescript-eslint/no-unused-vars
   27:42  warning  'createFreshProfileImageUrl' is defined but never used           @typescript-eslint/no-unused-vars
   63:19  warning  'isDarkMode' is assigned a value but never used                  @typescript-eslint/no-unused-vars
  588:30  error    `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\profile\FollowersFollowingScreen.tsx
  24:11  error    An empty interface declaration allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowInterfaces' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
  27:19  warning  'isDarkMode' is assigned a value but never used                                                                             
                                                                                                                                              
                                                                                                                @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\profile\InstagramProfileScreen.tsx
   1:40  warning  'useEffect' is defined but never used              @typescript-eslint/no-unused-vars
  58:12  warning  'profileError' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\profile\PostViewerScreen.tsx
   12:3   warning  'Modal' is defined but never used                                                                                          
                                                                                                                                              
                                                                                                                 @typescript-eslint/no-unused-vars
   23:10  warning  'getTimeAgo' is defined but never used                                                                                     
                                                                                                                                              
                                                                                                                 @typescript-eslint/no-unused-vars
   25:8   warning  'UserProfileScreen' is defined but never used                                                                              
                                                                                                                                              
                                                                                                                 @typescript-eslint/no-unused-vars
   26:10  warning  'API_BASE_URL' is defined but never used                                                                                   
                                                                                                                                              
                                                                                                                 @typescript-eslint/no-unused-vars
   30:9   warning  'width' is assigned a value but never used                                                                                 
                                                                                                                                              
                                                                                                                 @typescript-eslint/no-unused-vars
   32:11  error    An empty interface declaration allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowInterfaces' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type
   40:50  warning  'userId' is assigned a value but never used                                                                                
                                                                                                                                              
                                                                                                                 @typescript-eslint/no-unused-vars
  422:11  error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free                                                                                                                                                      
                                                                                                                 @typescript-eslint/ban-ts-comment

C:\A1\adtip-reactnative\Adtip\src\screens\profile\UserProfileScreen.tsx
   57:19  warning  'isDarkMode' is assigned a value but never used           @typescript-eslint/no-unused-vars
   79:28  warning  'setImageViewerIndex' is assigned a value but never used  @typescript-eslint/no-unused-vars
  429:14  warning  'e' is defined but never used                             @typescript-eslint/no-unused-vars
  429:17  error    Empty block statement                                     no-empty

C:\A1\adtip-reactnative\Adtip\src\screens\referral\ReferralScreen.tsx
  44:9   warning  'navigation' is assigned a value but never used  @typescript-eslint/no-unused-vars
  59:10  warning  'isLoading' is assigned a value but never used   @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\search\SearchScreen.tsx
    8:3   warning  'FlatList' is defined but never used                             @typescript-eslint/no-unused-vars
   33:10  warning  'user' is assigned a value but never used                        @typescript-eslint/no-unused-vars
  235:34  error    `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  235:48  error    `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\settings\PermissionsScreen.tsx
  7:3  warning  'Switch' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\settings\SettingsScreen.tsx
   20:10  warning  'getSetting' is defined but never used                    @typescript-eslint/no-unused-vars
   35:30  warning  'toggleTheme' is assigned a value but never used          @typescript-eslint/no-unused-vars
   38:9   warning  'userId' is assigned a value but never used               @typescript-eslint/no-unused-vars
   56:10  warning  'subscriptionLoading' is assigned a value but never used  @typescript-eslint/no-unused-vars
  218:9   warning  'handleDeleteAccount' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\support\SupportScreen.tsx
  123:81  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\tipcall\MissedCallsScreen.tsx
    1:40  warning  'useRef' is defined but never used                               @typescript-eslint/no-unused-vars
  289:16  error    `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\tipshop\TipShopScreen.tsx
  142:5  error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
  148:5  error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
  154:5  error  Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment

C:\A1\adtip-reactnative\Adtip\src\screens\tipshorts\TipShortsEnhanced.tsx
  74:24  error  Component definition is missing display name  react/display-name

C:\A1\adtip-reactnative\Adtip\src\screens\tipshorts\components\EnhancedShortCard.tsx
    1:57  warning  'useMemo' is defined but never used                                      @typescript-eslint/no-unused-vars
   13:8   warning  'Icon' is defined but never used                                         @typescript-eslint/no-unused-vars
   45:30  error    Component definition is missing display name                             react/display-name
   70:17  error    A `require()` style import is forbidden                                  @typescript-eslint/no-require-imports
  228:26  error    Component definition is missing display name                             react/display-name
  251:26  error    Component definition is missing display name                             react/display-name
  269:28  error    Component definition is missing display name                             react/display-name
  301:61  error    Component definition is missing display name                             react/display-name
  303:3   warning  'index' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\tiptube\AnimatedVideoCard.tsx
   11:17  warning  'VideoRef' is defined but never used                                          @typescript-eslint/no-unused-vars
   29:16  warning  'SCREEN_WIDTH' is assigned a value but never used                             @typescript-eslint/no-unused-vars
   90:27  error    Component definition is missing display name                                  react/display-name
   98:3   warning  'colors' is defined but never used. Allowed unused args must match /^_/u      @typescript-eslint/no-unused-vars
  399:7   warning  'createStyles' is assigned a value but never used                             @typescript-eslint/no-unused-vars
  399:36  warning  'isDarkMode' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\tiptube\ContentCreatorSubscriptionScreen.tsx
    5:3   warning  'StyleSheet' is defined but never used                                 @typescript-eslint/no-unused-vars
   19:8   warning  'LinearGradient' is defined but never used                             @typescript-eslint/no-unused-vars
   22:9   warning  'width' is assigned a value but never used                             @typescript-eslint/no-unused-vars
   48:16  warning  'error' is defined but never used                                      @typescript-eslint/no-unused-vars
  150:33  warning  'idx' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\tiptube\FollowedChannelScreen.tsx
  183:18  error  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

C:\A1\adtip-reactnative\Adtip\src\screens\tiptube\TipTubeScreen.tsx
    14:3   warning  'StatusBar' is defined but never used                                         @typescript-eslint/no-unused-vars
    15:3   warning  'SafeAreaView' is defined but never used                                      @typescript-eslint/no-unused-vars
    16:3   warning  'Switch' is defined but never used                                            @typescript-eslint/no-unused-vars
    17:3   warning  'Modal' is defined but never used                                             @typescript-eslint/no-unused-vars
    18:3   warning  'ViewToken' is defined but never used                                         @typescript-eslint/no-unused-vars
    23:8   warning  'Animated' is defined but never used                                          @typescript-eslint/no-unused-vars
    23:20  warning  'FadeIn' is defined but never used                                            @typescript-eslint/no-unused-vars
    25:10  warning  'CirclePlay' is defined but never used                                        @typescript-eslint/no-unused-vars
    26:8   warning  'RazorpayCheckout' is defined but never used                                  @typescript-eslint/no-unused-vars
    38:8   warning  'Header' is defined but never used                                            @typescript-eslint/no-unused-vars
    41:8   warning  'AnimatedVideoCard' is defined but never used                                 @typescript-eslint/no-unused-vars
    47:3   warning  'getSecureMediaUrl' is defined but never used                                 @typescript-eslint/no-unused-vars
    53:10  warning  'API_BASE_URL' is defined but never used                                      @typescript-eslint/no-unused-vars
    54:8   warning  'AsyncStorage' is defined but never used                                      @typescript-eslint/no-unused-vars
    56:8   warning  'ContentCreatorPlanToggle' is defined but never used                          @typescript-eslint/no-unused-vars
    60:8   warning  'useSimpleRewardedAd' is defined but never used                               @typescript-eslint/no-unused-vars
    66:38  warning  'SCREEN_HEIGHT' is assigned a value but never used                            @typescript-eslint/no-unused-vars
    75:7   warning  'REWARD_INTERVAL' is assigned a value but never used                          @typescript-eslint/no-unused-vars
    76:7   warning  'NON_PREMIUM_REWARD' is assigned a value but never used                       @typescript-eslint/no-unused-vars
    77:7   warning  'PREMIUM_REWARD' is assigned a value but never used                           @typescript-eslint/no-unused-vars
    98:11  warning  'CardLayout' is defined but never used                                        @typescript-eslint/no-unused-vars
   184:9   warning  'queryClient' is assigned a value but never used                              @typescript-eslint/no-unused-vars
   199:10  warning  'previewingVideoId' is assigned a value but never used                        @typescript-eslint/no-unused-vars
   199:29  warning  'setPreviewingVideoId' is assigned a value but never used                     @typescript-eslint/no-unused-vars
   205:29  warning  'setShowChannelVideos' is assigned a value but never used                     @typescript-eslint/no-unused-vars
   206:10  warning  'showPlanModal' is assigned a value but never used                            @typescript-eslint/no-unused-vars
   206:25  warning  'setShowPlanModal' is assigned a value but never used                         @typescript-eslint/no-unused-vars
   213:10  warning  'offerwallLoading' is assigned a value but never used                         @typescript-eslint/no-unused-vars
   223:5   warning  'refreshContentCreatorPremiumStatus' is assigned a value but never used       @typescript-eslint/no-unused-vars
   224:16  warning  'contentCreatorPremiumLoading' is assigned a value but never used             @typescript-eslint/no-unused-vars
   263:33  warning  'id' is defined but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
   299:16  warning  'channelLoading' is assigned a value but never used                           @typescript-eslint/no-unused-vars
   331:9   warning  'toggleComments' is assigned a value but never used                           @typescript-eslint/no-unused-vars
   331:39  warning  'videoId' is defined but never used. Allowed unused args must match /^_/u     @typescript-eslint/no-unused-vars
   340:9   warning  'handleLikeVideo' is assigned a value but never used                          @typescript-eslint/no-unused-vars
   491:9   warning  'handleTipTubeSearchChange' is assigned a value but never used                @typescript-eslint/no-unused-vars
   502:9   warning  'handleMyChannel' is assigned a value but never used                          @typescript-eslint/no-unused-vars
   566:9   warning  'handleAnalytics' is assigned a value but never used                          @typescript-eslint/no-unused-vars
   592:9   warning  'handleNavigateToYourChannel' is assigned a value but never used              @typescript-eslint/no-unused-vars
   600:9   warning  'handleNavigateToFollowedChannels' is assigned a value but never used         @typescript-eslint/no-unused-vars
   657:10  warning  'videoCount' is assigned a value but never used                               @typescript-eslint/no-unused-vars
   864:9   warning  'renderEarnCardsRow' is assigned a value but never used                       @typescript-eslint/no-unused-vars
   934:32  error    `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`               react/no-unescaped-entities
   934:53  error    `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`               react/no-unescaped-entities
   948:48  warning  'index' is defined but never used. Allowed unused args must match /^_/u       @typescript-eslint/no-unused-vars
   980:9   warning  'handleTogglePremium' is assigned a value but never used                      @typescript-eslint/no-unused-vars
  1001:12  warning  'liveSearchError' is assigned a value but never used                          @typescript-eslint/no-unused-vars
  1209:43  warning  'isDarkMode' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\tiptube\VideoPlayerModalScreen.tsx
   13:3   warning  'Image' is defined but never used                                        @typescript-eslint/no-unused-vars
   79:18  warning  'cardLayout' is assigned a value but never used                          @typescript-eslint/no-unused-vars
  389:36  warning  'error' is defined but never used                                        @typescript-eslint/no-unused-vars
  519:64  warning  'index' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\tiptube\YourChannelScreen.tsx
  18:8  warning  'TipTubeHeader' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\videosdk\MeetingScreenSimple.tsx
   21:10  warning  'ParticipantView' is defined but never used                      @typescript-eslint/no-unused-vars
   27:24  warning  'CallSession' is defined but never used                          @typescript-eslint/no-unused-vars
  112:9   warning  'navigation' is assigned a value but never used                  @typescript-eslint/no-unused-vars
  113:11  warning  'status' is assigned a value but never used                      @typescript-eslint/no-unused-vars
  114:36  warning  'leave' is assigned a value but never used                       @typescript-eslint/no-unused-vars
  192:43  warning  'join' is assigned a value but never used                        @typescript-eslint/no-unused-vars
  192:49  warning  'leave' is assigned a value but never used                       @typescript-eslint/no-unused-vars
  486:5   error    Definition for rule 'react-hooks/exhaustive-deps' was not found  react-hooks/exhaustive-deps
  504:25  error    A `require()` style import is forbidden                          @typescript-eslint/no-require-imports
  632:3   error    Unexpected var, use let or const instead                         no-var
  641:9   warning  'route' is assigned a value but never used                       @typescript-eslint/no-unused-vars
  644:9   warning  'navigation' is assigned a value but never used                  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\wallet\AddFundsScreen.tsx
    3:1   error    Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free  @typescript-eslint/ban-ts-comment
   18:31  error    A `require()` style import is forbidden                                                                              @typescript-eslint/no-require-imports
   19:16  warning  'height' is assigned a value but never used                                                                          @typescript-eslint/no-unused-vars
  134:17  warning  'err' is defined but never used. Allowed unused args must match /^_/u                                                @typescript-eslint/no-unused-vars
  198:45  warning  'index' is defined but never used. Allowed unused args must match /^_/u                                              @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\wallet\UpgradePremiumScreen.tsx
   19:31  error    A `require()` style import is forbidden                                @typescript-eslint/no-require-imports
  125:17  warning  'err' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\wallet\UserPremiumPlans.tsx
   2:95  warning  'Alert' is defined but never used             @typescript-eslint/no-unused-vars
   5:8   warning  'AsyncStorage' is defined but never used      @typescript-eslint/no-unused-vars
   8:8   warning  'WalletService' is defined but never used     @typescript-eslint/no-unused-vars
  35:10  warning  'loading' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\wallet\WalletScreen.tsx
  20:8   warning  'RazorpayCheckout' is defined but never used                 @typescript-eslint/no-unused-vars
  25:8   warning  'ApiService' is defined but never used                       @typescript-eslint/no-unused-vars
  41:7   warning  'RAZORPAY_KEY_ID' is assigned a value but never used         @typescript-eslint/no-unused-vars
  46:16  warning  'premiumState' is assigned a value but never used            @typescript-eslint/no-unused-vars
  50:11  warning  'userData' is assigned a value but never used                @typescript-eslint/no-unused-vars
  50:32  warning  'userDataLoading' is assigned a value but never used         @typescript-eslint/no-unused-vars
  50:58  warning  'refetchUserData' is assigned a value but never used         @typescript-eslint/no-unused-vars
  51:22  warning  'premiumExpiresAt' is assigned a value but never used        @typescript-eslint/no-unused-vars
  52:26  warning  'totalWithdrawals' is assigned a value but never used        @typescript-eslint/no-unused-vars
  56:31  warning  'setIsProcessingPayment' is assigned a value but never used  @typescript-eslint/no-unused-vars
  63:10  warning  'balanceError' is assigned a value but never used            @typescript-eslint/no-unused-vars
  63:24  warning  'setBalanceError' is assigned a value but never used         @typescript-eslint/no-unused-vars
  64:10  warning  'premiumError' is assigned a value but never used            @typescript-eslint/no-unused-vars
  64:24  warning  'setPremiumError' is assigned a value but never used         @typescript-eslint/no-unused-vars
  65:10  warning  'transactionsError' is assigned a value but never used       @typescript-eslint/no-unused-vars
  65:29  warning  'setTransactionsError' is assigned a value but never used    @typescript-eslint/no-unused-vars
  66:10  warning  'withdrawalsError' is assigned a value but never used        @typescript-eslint/no-unused-vars
  66:28  warning  'setWithdrawalsError' is assigned a value but never used     @typescript-eslint/no-unused-vars
  69:10  warning  'dataFetched' is assigned a value but never used             @typescript-eslint/no-unused-vars
  69:23  warning  'setDataFetched' is assigned a value but never used          @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\wallet\WithdrawalConfirmationScreen.tsx
  74:14  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\screens\wallet\WithdrawalMethodScreen.tsx
  113:19  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  122:19  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  131:19  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\services\ApiService.ts
    25:3   warning  'ReferralDetailsResponse' is defined but never used          @typescript-eslint/no-unused-vars
    26:3   warning  'FcmTokenRequest' is defined but never used                  @typescript-eslint/no-unused-vars
    27:3   warning  'MissedCallsResponse' is defined but never used              @typescript-eslint/no-unused-vars
    28:3   warning  'VideoSDKGenerateTokenRequest' is defined but never used     @typescript-eslint/no-unused-vars
    29:3   warning  'VideoSDKGenerateTokenResponse' is defined but never used    @typescript-eslint/no-unused-vars
    30:3   warning  'VideoSDKCreateMeetingRequest' is defined but never used     @typescript-eslint/no-unused-vars
    32:3   warning  'VideoSDKDeactivateRoomRequest' is defined but never used    @typescript-eslint/no-unused-vars
    33:3   warning  'VideoSDKDeactivateRoomResponse' is defined but never used   @typescript-eslint/no-unused-vars
    34:3   warning  'VideoSDKValidateMeetingRequest' is defined but never used   @typescript-eslint/no-unused-vars
    35:3   warning  'VideoSDKValidateMeetingResponse' is defined but never used  @typescript-eslint/no-unused-vars
  1120:11  warning  'url' is never reassigned. Use 'const' instead               prefer-const

C:\A1\adtip-reactnative\Adtip\src\services\CloudflareUploadService.ts
   81:24  error    Promise executor functions should not be async                            no-async-promise-executor
  116:65  warning  'userId' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  185:15  warning  'str' is never reassigned. Use 'const' instead                            prefer-const

C:\A1\adtip-reactnative\Adtip\src\services\FCMChatService.ts
  22:21  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\services\FCMChatServiceLocal.ts
  16:18  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type
  17:18  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type

C:\A1\adtip-reactnative\Adtip\src\services\FCMMessageRouter.ts
  203:11  warning  'chatManager' is never reassigned. Use 'const' instead                     prefer-const
  228:62  warning  'context' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\FirebaseService.ts
  403:42  warning  'categories' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\MessageCleanupService.ts
  202:14  error  Unexpected constant condition  no-constant-condition

C:\A1\adtip-reactnative\Adtip\src\services\PermissionManagerService.ts
   16:8   warning  'AsyncStorage' is defined but never used  @typescript-eslint/no-unused-vars
  367:28  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports
  379:40  error    A `require()` style import is forbidden   @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\services\PubScaleService.ts
   9:8   warning  'AnalyticsService' is defined but never used                                 @typescript-eslint/no-unused-vars
  50:59  warning  'onReward' is defined but never used. Allowed unused args must match /^_/u   @typescript-eslint/no-unused-vars
  50:81  warning  'onFailure' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\VideoCompressionService.ts
   92:11  warning  'compressionOptions' is never reassigned. Use 'const' instead                        prefer-const
  338:43  warning  'durationInSeconds' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars     

C:\A1\adtip-reactnative\Adtip\src\services\__tests__\CloudflareUploadService.test.ts
  60:30  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\services\calling\BackgroundCallHandler.ts
    9:13  warning  'NavigationService' is defined but never used  @typescript-eslint/no-unused-vars
   15:25  error    A `require()` style import is forbidden        @typescript-eslint/no-require-imports
  301:17  error    Unexpected lexical declaration in case block   no-case-declarations
  311:17  error    Unexpected lexical declaration in case block   no-case-declarations
  399:18  warning  'e' is defined but never used                  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\BackgroundCallTester.ts
  1:10  warning  'AppState' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\BackgroundMediaService.ts
   1:20  warning  'Platform' is defined but never used              @typescript-eslint/no-unused-vars
  10:29  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports
  11:3   warning  'request' is assigned a value but never used      @typescript-eslint/no-unused-vars
  12:3   warning  'PERMISSIONS' is assigned a value but never used  @typescript-eslint/no-unused-vars
  13:3   warning  'RESULTS' is assigned a value but never used      @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\CallBillingService.ts
  13:8   warning  'WalletService' is defined but never used  @typescript-eslint/no-unused-vars
  14:10  warning  'useCallStore' is defined but never used   @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\CallController.ts
    3:8   warning  'uuid' is defined but never used                                              @typescript-eslint/no-unused-vars
    9:3   error    Unexpected var, use let or const instead                                      no-var
   17:13  warning  'NavigationService' is defined but never used                                 @typescript-eslint/no-unused-vars
  133:16  warning  'prevStatus' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  295:21  error    A `require()` style import is forbidden                                       @typescript-eslint/no-require-imports        
  364:21  error    A `require()` style import is forbidden                                       @typescript-eslint/no-require-imports        
  383:19  error    A `require()` style import is forbidden                                       @typescript-eslint/no-require-imports        
  653:9   warning  'session' is never reassigned. Use 'const' instead                            prefer-const

C:\A1\adtip-reactnative\Adtip\src\services\calling\CallKitService.ts
  86:66  warning  'callData' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\CallSignalingService.ts
  1:8  warning  'messaging' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\CallSystemValidator.ts
    1:27  warning  'AppState' is defined but never used                      @typescript-eslint/no-unused-vars
  158:40  error    A `require()` style import is forbidden                   @typescript-eslint/no-require-imports
  270:13  warning  'mockCallData' is assigned a value but never used         @typescript-eslint/no-unused-vars
  279:13  warning  'notificationService' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\MediaService.ts
  1:10  warning  'MeetingProvider' is defined but never used  @typescript-eslint/no-unused-vars
  1:27  warning  'useMeeting' is defined but never used       @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\NotificationPersistenceService.ts
  2:10  warning  'Platform' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\calling\ReliableCallManager.ts
  2:8   warning  'messaging' is defined but never used  @typescript-eslint/no-unused-vars
  4:20  warning  'Platform' is defined but never used   @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\notification\NotificationFCMHandler.ts
  55:16  warning  'e' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\services\videosdk\VideoSDKService.ts
  141:51  warning  'participantToken' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars      

C:\A1\adtip-reactnative\Adtip\src\stores\tipShortsStore.ts
  102:17  warning  'get' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\tests\performance.test.js
   13:8   warning  'UserProfileScreen' is defined but never used     @typescript-eslint/no-unused-vars
   16:8   warning  'TipCallScreen' is defined but never used         @typescript-eslint/no-unused-vars
   20:13  error    'jest' is not defined                             no-undef
   21:11  error    'jest' is not defined                             no-undef
   22:15  error    'jest' is not defined                             no-undef
   56:1   error    'describe' is not defined                         no-undef
   57:7   warning  'queryClient' is assigned a value but never used  @typescript-eslint/no-unused-vars
   59:3   error    'beforeEach' is not defined                       no-undef
   61:5   error    'jest' is not defined                             no-undef
   64:3   error    'test' is not defined                             no-undef
   65:23  error    'performance' is not defined                      no-undef
   69:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
   69:10  error    'ProfileScreen' is not defined                    no-undef
   74:21  error    'performance' is not defined                      no-undef
   77:5   error    'expect' is not defined                           no-undef
   81:7   error    'expect' is not defined                           no-undef
   85:3   error    'test' is not defined                             no-undef
   86:23  error    'performance' is not defined                      no-undef
   94:21  error    'performance' is not defined                      no-undef
   97:5   error    'expect' is not defined                           no-undef
   98:5   error    'expect' is not defined                           no-undef
  101:3   error    'test' is not defined                             no-undef
  106:25  error    'performance' is not defined                      no-undef
  113:23  error    'performance' is not defined                      no-undef
  119:7   error    'expect' is not defined                           no-undef
  124:5   error    'expect' is not defined                           no-undef
  128:1   error    'describe' is not defined                         no-undef
  129:3   error    'test' is not defined                             no-undef
  137:7   error    'expect' is not defined                           no-undef
  143:23  error    'performance' is not defined                      no-undef
  147:21  error    'performance' is not defined                      no-undef
  150:5   error    'expect' is not defined                           no-undef
  153:3   error    'test' is not defined                             no-undef
  156:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  156:10  error    'ProfileScreen' is not defined                    no-undef
  161:7   error    'expect' is not defined                           no-undef
  166:23  error    'performance' is not defined                      no-undef
  169:21  error    'performance' is not defined                      no-undef
  172:5   error    'expect' is not defined                           no-undef
  176:1   error    'describe' is not defined                         no-undef
  177:3   error    'test' is not defined                             no-undef
  183:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  183:10  error    'ProfileScreen' is not defined                    no-undef
  194:23  error    'performance' is not defined                      no-undef
  198:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  198:10  error    'ProfileScreen' is not defined                    no-undef
  202:21  error    'performance' is not defined                      no-undef
  205:5   error    'expect' is not defined                           no-undef
  208:3   error    'test' is not defined                             no-undef
  209:24  error    'jest' is not defined                             no-undef
  213:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  213:10  error    'ProfileScreen' is not defined                    no-undef
  218:5   error    'expect' is not defined                           no-undef
  222:1   error    'describe' is not defined                         no-undef
  223:3   error    'test' is not defined                             no-undef
  224:27  error    'performance' is not defined                      no-undef
  230:12  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  230:12  error    'ProfileScreen' is not defined                    no-undef
  242:25  error    'performance' is not defined                      no-undef
  246:5   error    'expect' is not defined                           no-undef
  250:1   error    'describe' is not defined                         no-undef
  251:3   error    'test' is not defined                             no-undef
  253:20  error    'jest' is not defined                             no-undef
  262:23  error    'performance' is not defined                      no-undef
  266:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  266:10  error    'ProfileScreen' is not defined                    no-undef
  271:21  error    'performance' is not defined                      no-undef
  274:5   error    'expect' is not defined                           no-undef
  275:5   error    'expect' is not defined                           no-undef
  278:3   error    'test' is not defined                             no-undef
  284:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  284:10  error    'ProfileScreen' is not defined                    no-undef
  295:20  error    'jest' is not defined                             no-undef
  300:10  error    'ProfileScreen' is not defined                    react/jsx-no-undef
  300:10  error    'ProfileScreen' is not defined                    no-undef
  304:5   error    'expect' is not defined                           no-undef
  311:23  error    'performance' is not defined                      no-undef
  313:21  error    'performance' is not defined                      no-undef

C:\A1\adtip-reactnative\Adtip\src\types\api.ts
  413:18  error  An empty interface declaration allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowInterfaces' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

C:\A1\adtip-reactnative\Adtip\src\types\navigation.ts
  6:11  warning  'Comment' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\types\react-native-vector-icons.d.ts
   5:20  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type
  14:20  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type
  23:20  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type
  32:20  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type
  41:20  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type

C:\A1\adtip-reactnative\Adtip\src\utils\PerformanceUtils.ts
  294:46  warning  'T' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\__tests__\mediaUtils.test.ts
  43:37  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  85:37  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\utils\callStateCleanup.ts
   12:3   error    Unexpected var, use let or const instead  no-var
   13:3   error    Unexpected var, use let or const instead  no-var
   14:3   error    Unexpected var, use let or const instead  no-var
   21:3   error    Unexpected var, use let or const instead  no-var
   22:3   error    Unexpected var, use let or const instead  no-var
   23:3   error    Unexpected var, use let or const instead  no-var
   28:3   error    Unexpected var, use let or const instead  no-var
  179:18  warning  'e' is defined but never used             @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\callSystemIntegrationCheck.ts
  123:40  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\src\utils\dateUtils.ts
  44:12  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars
  60:12  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\mediaUtils.ts
   88:7  warning  'cleanUrl' is never reassigned. Use 'const' instead  prefer-const
  155:7  warning  'source' is never reassigned. Use 'const' instead    prefer-const
  188:7  warning  'source' is never reassigned. Use 'const' instead    prefer-const

C:\A1\adtip-reactnative\Adtip\src\utils\settingsStorage.ts
  48:22  warning  'storageKey' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\testDeepLinks.ts
  77:22  warning  'shareService' is assigned a value but never used      @typescript-eslint/no-unused-vars
  81:11  warning  'postShareOptions' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\timeUtils.ts
  141:12  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars
  158:12  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\tiptubeTheme.ts
  148:50  warning  'isDarkMode' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\src\utils\videoUtils.ts
  36:7   warning  'videoRef' is assigned a value but never used    @typescript-eslint/no-unused-vars
  40:13  warning  'videoProps' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\tailwind.config.js
  4:13  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\test-blank-screen-fix.js
  105:13  warning  'temp' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\test-callkeep-initialization.js
    8:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
    9:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
   17:32  error  '__dirname' is not defined               no-undef
   73:33  error  '__dirname' is not defined               no-undef
  129:32  error  '__dirname' is not defined               no-undef
  169:32  error  '__dirname' is not defined               no-undef

C:\A1\adtip-reactnative\Adtip\test-fcm-integration.js
  10:12  error    A `require()` style import is forbidden    @typescript-eslint/no-require-imports
  11:7   warning  'path' is assigned a value but never used  @typescript-eslint/no-unused-vars
  11:14  error    A `require()` style import is forbidden    @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\test-login-prompt-fix.js
   7:12  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
   8:14  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  13:40  error  '__dirname' is not defined               no-undef
  42:32  error  '__dirname' is not defined               no-undef

C:\A1\adtip-reactnative\Adtip\test_route_check.js
  4:15  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\test_saveVideoLike.js
  4:15  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\testing\e2e\CallKeepIntegration.e2e.ts
  212:18  warning  'e' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\testing\integration\CallFlow.test.ts
   23:7   warning  'callHandler' is assigned a value but never used  @typescript-eslint/no-unused-vars
   28:5   error    Unnecessary semicolon                             no-extra-semi
  133:27  error    A `require()` style import is forbidden           @typescript-eslint/no-require-imports

C:\A1\adtip-reactnative\Adtip\testing\performance\CallPerformance.test.ts
   14:7  warning  'callHandler' is assigned a value but never used  @typescript-eslint/no-unused-vars
   19:5  error    Unnecessary semicolon                             no-extra-semi
  119:7  error    Unnecessary semicolon                             no-extra-semi
  306:7  error    Unnecessary semicolon                             no-extra-semi
  319:7  error    Unnecessary semicolon                             no-extra-semi

C:\A1\adtip-reactnative\Adtip\testing\scripts\runAllTests.js
    8:9   warning  'execSync' is assigned a value but never used  @typescript-eslint/no-unused-vars
    8:29  error    A `require()` style import is forbidden        @typescript-eslint/no-require-imports
    9:12  error    A `require()` style import is forbidden        @typescript-eslint/no-require-imports
   10:14  error    A `require()` style import is forbidden        @typescript-eslint/no-require-imports
  158:13  warning  'result' is assigned a value but never used    @typescript-eslint/no-unused-vars
  196:34  error    '__dirname' is not defined                     no-undef

C:\A1\adtip-reactnative\Adtip\testing\setup\jest.setup.js
    6:1   error  'jest' is not defined                    no-undef
    9:13  error  'jest' is not defined                    no-undef
   12:17  error  'jest' is not defined                    no-undef
   13:14  error  'jest' is not defined                    no-undef
   14:23  error  'jest' is not defined                    no-undef
   15:26  error  'jest' is not defined                    no-undef
   19:23  error  'jest' is not defined                    no-undef
   20:26  error  'jest' is not defined                    no-undef
   23:10  error  'jest' is not defined                    no-undef
   26:12  error  'jest' is not defined                    no-undef
   29:14  error  'jest' is not defined                    no-undef
   30:12  error  'jest' is not defined                    no-undef
   43:1   error  'jest' is not defined                    no-undef
   45:12  error  'jest' is not defined                    no-undef
   46:20  error  'jest' is not defined                    no-undef
   47:23  error  'jest' is not defined                    no-undef
   48:27  error  'jest' is not defined                    no-undef
   49:24  error  'jest' is not defined                    no-undef
   50:15  error  'jest' is not defined                    no-undef
   51:16  error  'jest' is not defined                    no-undef
   52:30  error  'jest' is not defined                    no-undef
   53:29  error  'jest' is not defined                    no-undef
   54:34  error  'jest' is not defined                    no-undef
   66:1   error  'jest' is not defined                    no-undef
   67:12  error  'jest' is not defined                    no-undef
   68:12  error  'jest' is not defined                    no-undef
   69:15  error  'jest' is not defined                    no-undef
   70:10  error  'jest' is not defined                    no-undef
   71:15  error  'jest' is not defined                    no-undef
   72:13  error  'jest' is not defined                    no-undef
   73:13  error  'jest' is not defined                    no-undef
   74:16  error  'jest' is not defined                    no-undef
   78:1   error  'jest' is not defined                    no-undef
   79:10  error  'jest' is not defined                    no-undef
   80:24  error  'jest' is not defined                    no-undef
   81:23  error  'jest' is not defined                    no-undef
   82:12  error  'jest' is not defined                    no-undef
   83:16  error  'jest' is not defined                    no-undef
   84:15  error  'jest' is not defined                    no-undef
   85:17  error  'jest' is not defined                    no-undef
   86:13  error  'jest' is not defined                    no-undef
   87:21  error  'jest' is not defined                    no-undef
   88:24  error  'jest' is not defined                    no-undef
   89:32  error  'jest' is not defined                    no-undef
   90:34  error  'jest' is not defined                    no-undef
   91:20  error  'jest' is not defined                    no-undef
   92:27  error  'jest' is not defined                    no-undef
   93:17  error  'jest' is not defined                    no-undef
   94:14  error  'jest' is not defined                    no-undef
   95:18  error  'jest' is not defined                    no-undef
   99:1   error  'jest' is not defined                    no-undef
  101:15  error  'jest' is not defined                    no-undef
  102:11  error  'jest' is not defined                    no-undef
  103:12  error  'jest' is not defined                    no-undef
  104:10  error  'jest' is not defined                    no-undef
  111:19  error  'jest' is not defined                    no-undef
  126:1   error  'jest' is not defined                    no-undef
  128:15  error  'jest' is not defined                    no-undef
  132:20  error  'jest' is not defined                    no-undef
  133:21  error  'jest' is not defined                    no-undef
  134:16  error  'jest' is not defined                    no-undef
  137:15  error  'jest' is not defined                    no-undef
  138:16  error  'jest' is not defined                    no-undef
  143:1   error  'jest' is not defined                    no-undef
  144:18  error  'jest' is not defined                    no-undef
  145:15  error  'jest' is not defined                    no-undef
  146:13  error  'jest' is not defined                    no-undef
  147:12  error  'jest' is not defined                    no-undef
  149:13  error  'jest' is not defined                    no-undef
  153:33  error  'jest' is not defined                    no-undef
  157:1   error  'jest' is not defined                    no-undef
  159:10  error  'jest' is not defined                    no-undef
  160:11  error  'jest' is not defined                    no-undef
  161:12  error  'jest' is not defined                    no-undef
  167:18  error  'jest' is not defined                    no-undef
  170:11  error  'jest' is not defined                    no-undef
  171:11  error  'jest' is not defined                    no-undef
  179:3   error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  183:3   error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  190:3   error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  201:20  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  216:17  error  'jest' is not defined                    no-undef
  220:18  error  'jest' is not defined                    no-undef
  224:19  error  'jest' is not defined                    no-undef
  281:1   error  'afterEach' is not defined               no-undef
  282:3   error  'jest' is not defined                    no-undef
  285:24  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  291:20  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  298:28  error  A `require()` style import is forbidden  @typescript-eslint/no-require-imports
  303:18  error  'jest' is not defined                    no-undef
  304:19  error  'jest' is not defined                    no-undef
  305:14  error  'jest' is not defined                    no-undef
  316:1   error  'jest' is not defined                    no-undef

C:\A1\adtip-reactnative\Adtip\testing\unit\CallKeepPermissions.test.ts
   32:22  error    A `require()` style import is forbidden                    @typescript-eslint/no-require-imports
  114:13  warning  'shouldUseCallKeepSpy' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\testing\unit\CallUICoordinator.test.ts
  157:7   error    Unnecessary semicolon                        no-extra-semi
  178:7   error    Unnecessary semicolon                        no-extra-semi
  185:7   error    Unnecessary semicolon                        no-extra-semi
  250:13  warning  'result' is assigned a value but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\testing\utils\TestingUtils.ts
  115:75  error    Unnecessary escape character: \/   no-useless-escape
  115:83  error    Unnecessary escape character: \/   no-useless-escape
  115:91  error    Unnecessary escape character: \/   no-useless-escape
  149:14  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars

C:\A1\adtip-reactnative\Adtip\verify-callkeep-fix.js
   8:12  error    A `require()` style import is forbidden                 @typescript-eslint/no-require-imports
   9:14  error    A `require()` style import is forbidden                 @typescript-eslint/no-require-imports
  53:5   warning  'totalChecks' is never reassigned. Use 'const' instead  prefer-const
  57:32  error    '__dirname' is not defined                              no-undef

C:\A1\adtip-reactnative\Adtip\verify-google-play-compliance.js
   10:12  error    A `require()` style import is forbidden  @typescript-eslint/no-require-imports
   11:14  error    A `require()` style import is forbidden  @typescript-eslint/no-require-imports
   74:12  warning  'error' is defined but never used        @typescript-eslint/no-unused-vars
  102:34  error    '__dirname' is not defined               no-undef
  163:32  error    '__dirname' is not defined               no-undef
  196:32  error    '__dirname' is not defined               no-undef

C:\A1\adtip-reactnative\Adtip\verify-reliable-call-flow.js
  6:12  error    A `require()` style import is forbidden    @typescript-eslint/no-require-imports
  7:7   warning  'path' is assigned a value but never used  @typescript-eslint/no-unused-vars
  7:14  error    A `require()` style import is forbidden    @typescript-eslint/no-require-imports

✖ 1064 problems (434 errors, 630 warnings)
  8 errors and 27 warnings potentially fixable with the `--fix` option.
